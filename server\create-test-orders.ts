import { storage } from './storage';

/**
 * Create test orders for demonstration purposes
 */
export async function createTestOrders() {
  try {
    console.log('Creating test orders...');

    // Create a regular order
    console.log('Creating regular order...');
    const regularOrder = await storage.createInvoice({
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '19.99',
      status: 'paid',
      country: 'United States',
      appType: 'IPTV Smarters Pro',
      createdAt: new Date().toISOString(),
      isTrialOrder: false,
      hasUpgraded: false,
      notes: 'Default regular order customer'
    });

    console.log('Regular order created:', regularOrder);

    // Create a trial order
    console.log('Creating trial order...');
    const trialOrder = await storage.createInvoice({
      customerName: 'Jane Do<PERSON>',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '4.99',
      status: 'paid',
      country: 'United Kingdom',
      appType: 'MAG',
      macAddress: '00:1A:79:B4:E7:2D',
      createdAt: new Date().toISOString(),
      isTrialOrder: true,
      hasUpgraded: false,
      notes: 'Default trial order customer'
    });

    console.log('Trial order created:', trialOrder);

    console.log('Test orders created successfully!');
    console.log('\nRegular Order Details:');
    console.log('- Customer: John Smith (<EMAIL>)');
    console.log('- Country: United States');
    console.log('- Application: IPTV Smarters Pro');
    console.log('- Amount: $19.99');
    console.log('- Status: paid');
    
    console.log('\nTrial Order Details:');
    console.log('- Customer: Jane Doe (<EMAIL>)');
    console.log('- Country: United Kingdom');
    console.log('- Application: MAG');
    console.log('- MAC Address: 00:1A:79:B4:E7:2D');
    console.log('- Amount: $4.99');
    console.log('- Status: paid');
    
    return { regularOrder, trialOrder };
  } catch (error) {
    console.error('Error creating test orders:', error);
    throw error;
  }
}
