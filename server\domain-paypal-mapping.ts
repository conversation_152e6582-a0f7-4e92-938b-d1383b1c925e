/**
 * Domain to PayPal account mapping
 * This file maps each domain to its specific PayPal configuration
 */

// Default configuration (used for the main domain)
export const defaultConfig = {
  paypalEmail: process.env.DEFAULT_PAYPAL_EMAIL || '<EMAIL>',
  clientId: process.env.DEFAULT_PAYPAL_CLIENT_ID || 'your-default-client-id',
  clientSecret: process.env.DEFAULT_PAYPAL_CLIENT_SECRET || 'your-default-client-secret',
  merchantId: process.env.DEFAULT_PAYPAL_MERCHANT_ID || 'your-default-merchant-id',
  buttonStyle: {
    color: 'gold',
    shape: 'rect',
    label: 'paypal',
    height: 40
  }
};

// Domain-specific configurations
// Replace these with your actual domain names and PayPal credentials
export const domainConfigs = {
  'domain1.com': {
    paypalEmail: process.env.DOMAIN1_PAYPAL_EMAIL || '<EMAIL>',
    clientId: process.env.DOMAIN1_PAYPAL_CLIENT_ID || 'client-id-for-domain1',
    clientSecret: process.env.DOMAIN1_PAYPAL_CLIENT_SECRET || 'client-secret-for-domain1',
    merchantId: process.env.DOMAIN1_PAYPAL_MERCHANT_ID || 'merchant-id-for-domain1',
    buttonStyle: {
      color: 'gold',
      shape: 'rect',
      label: 'paypal',
      height: 40
    }
  },
  'domain2.com': {
    paypalEmail: process.env.DOMAIN2_PAYPAL_EMAIL || '<EMAIL>',
    clientId: process.env.DOMAIN2_PAYPAL_CLIENT_ID || 'client-id-for-domain2',
    clientSecret: process.env.DOMAIN2_PAYPAL_CLIENT_SECRET || 'client-secret-for-domain2',
    merchantId: process.env.DOMAIN2_PAYPAL_MERCHANT_ID || 'merchant-id-for-domain2',
    buttonStyle: {
      color: 'blue',
      shape: 'rect',
      label: 'paypal',
      height: 40
    }
  }
  // Add more domains as needed
};

/**
 * Get PayPal configuration for a specific domain
 * @param {string} hostname - The hostname from the request
 * @returns {Object} PayPal configuration for the domain
 */
export function getPayPalConfigForDomain(hostname) {
  if (!hostname) return defaultConfig;

  // Check for exact domain match
  if (domainConfigs[hostname]) {
    return domainConfigs[hostname];
  }

  // Check for subdomain match
  for (const domain in domainConfigs) {
    if (hostname.endsWith('.' + domain)) {
      return domainConfigs[domain];
    }
  }

  // Return default config if no match found
  return defaultConfig;
}
