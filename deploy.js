#!/usr/bin/env node
/**
 * Deployment script for CloudPanel
 * 
 * This script:
 * 1. Builds the application in production mode
 * 2. Ensures all development dependencies are excluded
 * 3. Prepares the application for deployment
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Helper function to log with colors
function log(message, color = colors.fg.white) {
  console.log(`${color}${message}${colors.reset}`);
}

// Helper function to execute commands and log output
function execute(command, options = {}) {
  log(`\n${colors.bright}${colors.fg.cyan}Executing: ${command}${colors.reset}`);
  try {
    const output = execSync(command, { 
      stdio: 'inherit',
      ...options
    });
    return output;
  } catch (error) {
    log(`\n${colors.fg.red}Command failed: ${command}${colors.reset}`);
    log(`${colors.fg.red}${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Main function
async function deploy() {
  log(`\n${colors.bright}${colors.fg.green}=== Starting deployment process ===${colors.reset}`);
  
  // 1. Clean up previous build
  log(`\n${colors.fg.yellow}Cleaning up previous build...${colors.reset}`);
  if (fs.existsSync(path.join(__dirname, 'dist'))) {
    fs.rmSync(path.join(__dirname, 'dist'), { recursive: true, force: true });
  }
  
  // 2. Build the application in production mode
  log(`\n${colors.fg.yellow}Building application in production mode...${colors.reset}`);
  execute('npm run build:prod');
  
  // 3. Verify the build
  log(`\n${colors.fg.yellow}Verifying build...${colors.reset}`);
  if (!fs.existsSync(path.join(__dirname, 'dist', 'index.js'))) {
    log(`${colors.fg.red}Build failed: dist/index.js not found${colors.reset}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(path.join(__dirname, 'dist', 'public', 'index.html'))) {
    log(`${colors.fg.red}Build failed: dist/public/index.html not found${colors.reset}`);
    process.exit(1);
  }
  
  // 4. Create a production .env file if it doesn't exist
  const envPath = path.join(__dirname, '.env');
  const prodEnvPath = path.join(__dirname, '.env.production');
  
  if (!fs.existsSync(prodEnvPath)) {
    log(`\n${colors.fg.yellow}Creating production .env file...${colors.reset}`);
    if (fs.existsSync(envPath)) {
      let envContent = fs.readFileSync(envPath, 'utf8');
      
      // Update environment variables for production
      envContent = envContent
        .replace(/SECURE_COOKIES=false/g, 'SECURE_COOKIES=true')
        .replace(/NODE_ENV=development/g, 'NODE_ENV=production');
      
      fs.writeFileSync(prodEnvPath, envContent);
      log(`${colors.fg.green}Created .env.production file${colors.reset}`);
    } else {
      log(`${colors.fg.yellow}No .env file found, creating a basic one...${colors.reset}`);
      fs.writeFileSync(prodEnvPath, 
`DATABASE_URL=mysql://your_db_user:your_password@localhost:3306/your_db_name
SESSION_SECRET=your_secure_random_string
NODE_ENV=production
SECURE_COOKIES=true`);
    }
  }
  
  log(`\n${colors.bright}${colors.fg.green}=== Deployment build completed successfully ===${colors.reset}`);
  log(`\n${colors.fg.cyan}Next steps:${colors.reset}`);
  log(`1. Upload the files to your CloudPanel server`);
  log(`2. Update the .env file with your production database credentials`);
  log(`3. Install dependencies with: npm install --production`);
  log(`4. Start the application with: pm2 start dist/index.js --name digital-invoice`);
  log(`5. Set file permissions: find . -type d -exec chmod 755 {} \\; && find . -type f -exec chmod 644 {} \\; && chmod 755 dist/index.js`);
}

// Run the deployment process
deploy().catch(error => {
  log(`\n${colors.fg.red}Deployment failed: ${error.message}${colors.reset}`);
  process.exit(1);
});
