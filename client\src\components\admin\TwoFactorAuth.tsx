import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useLocation } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2, KeyRound } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Validation schema for authenticator code
const twoFactorSchema = z.object({
  token: z.string().min(6, 'Verification code must be at least 6 characters').max(8, 'Verification code must be at most 8 characters'),
});

// Validation schema for recovery code
const recoveryCodeSchema = z.object({
  code: z.string().min(8, 'Recovery code must be at least 8 characters'),
});

// Form data types
type TwoFactorFormData = z.infer<typeof twoFactorSchema>;
type RecoveryCodeFormData = z.infer<typeof recoveryCodeSchema>;

interface TwoFactorAuthProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export default function TwoFactorAuth({ onSuccess, onCancel }: TwoFactorAuthProps) {
  const { toast } = useToast();
  const { checkAuth } = useAdminAuth();
  const [, setLocation] = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('authenticator');

  // Form setup for authenticator
  const form = useForm<TwoFactorFormData>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      token: '',
    },
  });

  // Form setup for recovery code
  const recoveryForm = useForm<RecoveryCodeFormData>({
    resolver: zodResolver(recoveryCodeSchema),
    defaultValues: {
      code: '',
    },
  });

  // Submit handler for authenticator code
  const onSubmit = async (data: TwoFactorFormData) => {
    setIsSubmitting(true);
    try {
      await apiRequest('/api/admin/two-factor/verify', 'POST', data);

      toast({
        title: "Verification successful",
        description: "You have been successfully authenticated.",
      });

      console.log("2FA verification successful, redirecting to dashboard...");

      try {
        // Try to refresh auth state, but don't block if it fails
        if (checkAuth) {
          await checkAuth();
        }
      } catch (e) {
        console.error("Error refreshing auth state:", e);
      }

      // Call the onSuccess callback
      onSuccess();

      // Add a small delay before redirecting
      setTimeout(() => {
        // Force redirect to dashboard
        window.location.href = '/admin/dashboard';
      }, 500);
    } catch (error) {
      toast({
        title: "Verification failed",
        description: error instanceof Error ? error.message : "Invalid verification code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit handler for recovery code
  const onSubmitRecoveryCode = async (data: RecoveryCodeFormData) => {
    setIsSubmitting(true);
    try {
      await apiRequest('/api/admin/recovery-codes/verify', 'POST', data);

      toast({
        title: "Recovery successful",
        description: "You have been successfully authenticated using a recovery code.",
      });

      console.log("Recovery code verification successful, redirecting to dashboard...");

      try {
        // Try to refresh auth state, but don't block if it fails
        if (checkAuth) {
          await checkAuth();
        }
      } catch (e) {
        console.error("Error refreshing auth state:", e);
      }

      // Call the onSuccess callback
      onSuccess();

      // Add a small delay before redirecting
      setTimeout(() => {
        // Force redirect to dashboard
        window.location.href = '/admin/dashboard';
      }, 500);
    } catch (error) {
      toast({
        title: "Recovery failed",
        description: error instanceof Error ? error.message : "Invalid recovery code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-2xl text-center">Two-Factor Authentication</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="authenticator" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="authenticator">Authenticator</TabsTrigger>
            <TabsTrigger value="recovery">Recovery Code</TabsTrigger>
          </TabsList>

          <TabsContent value="authenticator">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="token"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter code from your authenticator app"
                          {...field}
                          autoComplete="one-time-code"
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the 6-digit code from your authenticator app
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      "Verify"
                    )}
                  </Button>
                </div>
              </form>
            </Form>
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Open your authenticator app to view your verification code
              </p>
            </div>
          </TabsContent>

          <TabsContent value="recovery">
            <Form {...recoveryForm}>
              <form onSubmit={recoveryForm.handleSubmit(onSubmitRecoveryCode)} className="space-y-6">
                <FormField
                  control={recoveryForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recovery Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your recovery code"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter one of your recovery codes
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      "Verify"
                    )}
                  </Button>
                </div>
              </form>
            </Form>
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Use a recovery code if you've lost access to your authenticator app
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
