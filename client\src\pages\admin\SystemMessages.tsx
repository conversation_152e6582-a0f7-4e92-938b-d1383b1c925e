import { useState } from "react";
import { useSystemMessages } from "@/hooks/use-system-messages";
import AdminLayout from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Search } from "lucide-react";
import { SYSTEM_MESSAGE_CATEGORIES } from "@/lib/system-messages";
import SimpleRichTextEditor from "@/components/admin/SimpleRichTextEditor";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

export default function SystemMessages() {
  const { messages, isLoading, updateMessage } = useSystemMessages();
  const [activeTab, setActiveTab] = useState(SYSTEM_MESSAGE_CATEGORIES[0].id);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState("");
  const [isHtml, setIsHtml] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Filter messages by category and search query
  const filteredMessages = messages.filter(
    (msg) =>
      (activeTab === "all" || msg.category === activeTab) &&
      (searchQuery === "" ||
        msg.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        msg.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        msg.content.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle editing a message
  const handleEditMessage = (messageId: string) => {
    const message = messages.find((msg) => msg.id === messageId);
    if (message) {
      setEditingMessageId(messageId);
      setEditedContent(message.content);
      setIsHtml(message.isHtml);
    }
  };

  // Handle saving a message
  const handleSaveMessage = async () => {
    if (!editingMessageId) return;

    setIsSaving(true);
    try {
      await updateMessage(editingMessageId, {
        content: editedContent,
        isHtml
      });

      toast({
        title: "Message updated",
        description: "The system message has been updated successfully.",
      });

      setEditingMessageId(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update the system message.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingMessageId(null);
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">System Messages</CardTitle>
          <CardDescription>
            Manage and customize system messages displayed throughout the application
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search messages..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4 flex flex-wrap">
                  <TabsTrigger value="all">All Categories</TabsTrigger>
                  {SYSTEM_MESSAGE_CATEGORIES.map((category) => (
                    <TabsTrigger key={category.id} value={category.id}>
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value={activeTab} className="space-y-4">
                  {filteredMessages.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No messages found
                    </div>
                  ) : (
                    filteredMessages.map((message) => (
                      <Card key={message.id} className="overflow-hidden">
                        <CardHeader className="bg-muted/50 pb-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <CardTitle className="text-lg">{message.name}</CardTitle>
                              <CardDescription>{message.description}</CardDescription>
                            </div>
                            <Badge variant="outline">{message.category}</Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-4">
                          {editingMessageId === message.id ? (
                            <div className="space-y-4">
                              <div className="flex items-center space-x-2">
                                <Switch
                                  id={`html-${message.id}`}
                                  checked={isHtml}
                                  onCheckedChange={setIsHtml}
                                />
                                <Label htmlFor={`html-${message.id}`}>Enable HTML formatting</Label>
                              </div>

                              <SimpleRichTextEditor
                                initialValue={editedContent}
                                onChange={setEditedContent}
                                height={200}
                                showPreview={true}
                              />

                              <div className="flex justify-end space-x-2 mt-4">
                                <Button
                                  variant="outline"
                                  onClick={handleCancelEdit}
                                  disabled={isSaving}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={handleSaveMessage}
                                  disabled={isSaving}
                                >
                                  {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                  Save Changes
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <div className="border rounded-md p-3 min-h-[100px] bg-muted/30">
                                {message.isHtml ? (
                                  <div dangerouslySetInnerHTML={{ __html: message.content }} />
                                ) : (
                                  <p>{message.content}</p>
                                )}
                              </div>
                              <div className="flex justify-end">
                                <Button
                                  variant="outline"
                                  onClick={() => handleEditMessage(message.id)}
                                >
                                  Edit Message
                                </Button>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </TabsContent>
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
