import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Loader2,
  Upload,
  DownloadCloud,
  RefreshCw,
  Database,
  Archive,
  FileText,
  Check,
  X,
  AlertTriangle,
  FileSearch,
  Search,
  Trash
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { apiRequest } from '@/lib/queryClient';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

// Types
interface UpdateInfo {
  lastUpdate: string | null;
  updateHistory: UpdateHistoryItem[];
  systemInfo: {
    version: string;
    nodeVersion: string;
    platform: string;
    uptime: number;
  };
}

interface UpdateHistoryItem {
  id: string;
  date: string;
  version: string;
  status: 'success' | 'failed' | 'rolled-back';
  details: string;
}

interface BackupInfo {
  backups: BackupItem[];
  lastBackup: string | null;
  autoBackupEnabled: boolean;
}

interface BackupItem {
  id: string;
  date: string;
  size: string;
  type: 'manual' | 'auto' | 'pre-update';
  name: string;
}

interface FileRecord {
  path: string;
  checksum: string;
  lastModified: string | Date;
  status?: 'new' | 'modified' | 'deleted';
}

interface ChangedFilesResponse {
  changedFiles: FileRecord[];
  count: number;
}

// API functions
const getUpdateInfo = async (): Promise<UpdateInfo> => {
  return apiRequest('/api/admin/system/updates', 'GET');
};

const getBackupInfo = async (): Promise<BackupInfo> => {
  return apiRequest('/api/admin/system/backups', 'GET');
};

const getChangedFiles = async (): Promise<ChangedFilesResponse> => {
  return apiRequest('/api/admin/system/changed-files', 'GET');
};

const scanFiles = async (forceRescan: boolean = false): Promise<{ success: boolean; message: string }> => {
  return apiRequest('/api/admin/system/scan-files', 'POST', { forceRescan });
};

const resetFileTracker = async (): Promise<{ success: boolean; message: string; changedFiles: FileRecord[]; count: number }> => {
  return apiRequest('/api/admin/system/reset-tracker', 'POST');
};

const createUpdatePackage = async (data: { version: string; description: string; files: FileRecord[] }): Promise<Blob> => {
  const response = await fetch('/api/admin/system/create-package', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to create update package');
  }

  return response.blob();
};

// Export data functions
const exportProducts = async (): Promise<void> => {
  console.log('Exporting products...');
  window.location.href = '/api/admin/data/products';
};

const exportOrders = async (): Promise<void> => {
  window.location.href = '/api/admin/data/orders';
};

const exportSettings = async (): Promise<void> => {
  window.location.href = '/api/admin/data/settings';
};

const exportAll = async (): Promise<void> => {
  window.location.href = '/api/admin/data/all';
};

export default function SystemUpdates() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [createBackupDialogOpen, setCreateBackupDialogOpen] = useState(false);
  const [backupName, setBackupName] = useState('');
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);

  // File tracking and update package creation
  const [createUpdateDialogOpen, setCreateUpdateDialogOpen] = useState(false);
  const [updateVersion, setUpdateVersion] = useState('');
  const [updateDescription, setUpdateDescription] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [isCreatingPackage, setIsCreatingPackage] = useState(false);

  // Import data state
  const [importProductsDialogOpen, setImportProductsDialogOpen] = useState(false);
  const [importOrdersDialogOpen, setImportOrdersDialogOpen] = useState(false);
  const [importSettingsDialogOpen, setImportSettingsDialogOpen] = useState(false);
  const [importAllDialogOpen, setImportAllDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);

  // Backup upload state
  const [uploadBackupDialogOpen, setUploadBackupDialogOpen] = useState(false);
  const [backupFile, setBackupFile] = useState<File | null>(null);
  const [isUploadingBackup, setIsUploadingBackup] = useState(false);

  // Fetch update info
  const {
    data: updateInfo,
    isLoading: isLoadingUpdateInfo
  } = useQuery({
    queryKey: ['updateInfo'],
    queryFn: getUpdateInfo,
    // For now, we'll handle the error case with a placeholder
    // In a real implementation, you'd want proper error handling
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to fetch update information",
        variant: "destructive"
      });
    }
  });

  // Fetch backup info
  const {
    data: backupInfo,
    isLoading: isLoadingBackupInfo
  } = useQuery({
    queryKey: ['backupInfo'],
    queryFn: getBackupInfo,
    // For now, we'll handle the error case with a placeholder
    // In a real implementation, you'd want proper error handling
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to fetch backup information",
        variant: "destructive"
      });
    }
  });

  // Fetch changed files
  const {
    data: changedFilesData,
    isLoading: isLoadingChangedFiles,
    refetch: refetchChangedFiles
  } = useQuery({
    queryKey: ['changedFiles'],
    queryFn: getChangedFiles,
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to fetch changed files",
        variant: "destructive"
      });
    }
  });

  // Upload update mutation
  const uploadUpdateMutation = useMutation({
    mutationFn: async (file: File) => {
      setIsUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('updateFile', file);

      // Simulate progress for now
      // In a real implementation, you'd use XMLHttpRequest with progress events
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 95) {
            clearInterval(interval);
            return 95;
          }
          return prev + 5;
        });
      }, 300);

      try {
        // This would be a real API call in production
        // await apiRequest('/api/admin/system/updates/upload', 'POST', formData);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 3000));

        clearInterval(interval);
        setUploadProgress(100);

        return { success: true };
      } catch (error) {
        clearInterval(interval);
        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['updateInfo'] });
      setUploadDialogOpen(false);
      setSelectedFile(null);
      toast({
        title: "Success",
        description: "Update uploaded and applied successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to upload update",
        variant: "destructive"
      });
    }
  });

  // Create backup mutation
  const createBackupMutation = useMutation({
    mutationFn: async (name: string) => {
      setIsCreatingBackup(true);

      try {
        // Make a real API call to create the backup
        return await apiRequest('/api/admin/system/backups', 'POST', { name });
      } finally {
        setIsCreatingBackup(false);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backupInfo'] });
      setCreateBackupDialogOpen(false);
      setBackupName('');
      toast({
        title: "Success",
        description: "Backup created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create backup",
        variant: "destructive"
      });
    }
  });

  // Scan files mutation
  const scanFilesMutation = useMutation({
    mutationFn: async (forceRescan: boolean = false) => {
      setIsScanning(true);
      try {
        return await scanFiles(forceRescan);
      } finally {
        setIsScanning(false);
      }
    },
    onSuccess: (data, variables) => {
      refetchChangedFiles();
      toast({
        title: "Success",
        description: variables ? "Force rescan completed successfully" : "File scan completed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to scan files",
        variant: "destructive"
      });
    }
  });

  // Reset file tracker mutation
  const resetFileTrackerMutation = useMutation({
    mutationFn: async () => {
      setIsResetting(true);
      try {
        return await resetFileTracker();
      } finally {
        setIsResetting(false);
      }
    },
    onSuccess: (data) => {
      // Update the changed files data directly from the response
      // This avoids an extra API call and ensures we see the empty state immediately
      queryClient.setQueryData(['changedFiles'], data);

      toast({
        title: "Success",
        description: "File tracker reset successfully. All files have been cleared from tracking.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to reset file tracker",
        variant: "destructive"
      });
    }
  });

  // Create update package mutation
  const createUpdatePackageMutation = useMutation({
    mutationFn: async (data: { version: string; description: string; files: FileRecord[] }) => {
      setIsCreatingPackage(true);
      try {
        return await createUpdatePackage(data);
      } finally {
        setIsCreatingPackage(false);
      }
    },
    onSuccess: (blob) => {
      // Create a download link for the blob
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `update-${updateVersion}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setCreateUpdateDialogOpen(false);
      setUpdateVersion('');
      setUpdateDescription('');
      setSelectedFiles([]);

      toast({
        title: "Success",
        description: "Update package created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create update package",
        variant: "destructive"
      });
    }
  });

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
    }
  };

  // Handle update upload
  const handleUpload = () => {
    if (selectedFile) {
      uploadUpdateMutation.mutate(selectedFile);
    }
  };

  // Handle backup creation
  const handleCreateBackup = () => {
    if (backupName.trim()) {
      createBackupMutation.mutate(backupName);
    }
  };

  // Toggle all files selection
  const toggleAllFiles = () => {
    if (!changedFilesData) return;

    const allSelected = changedFilesData.changedFiles.length === selectedFiles.length;

    if (allSelected) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(changedFilesData.changedFiles.map(file => file.path));
    }
  };

  // Handle create update package
  const handleCreateUpdatePackage = () => {
    if (!changedFilesData) return;

    if (!updateVersion.trim()) {
      toast({
        title: "Error",
        description: "Please enter a version number",
        variant: "destructive"
      });
      return;
    }

    if (selectedFiles.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one file",
        variant: "destructive"
      });
      return;
    }

    const selectedFileRecords = changedFilesData.changedFiles.filter(
      file => selectedFiles.includes(file.path)
    );

    createUpdatePackageMutation.mutate({
      version: updateVersion,
      description: updateDescription,
      files: selectedFileRecords
    });
  };

  // Handle import file selection
  const handleImportFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setImportFile(files[0]);
    }
  };

  // Handle backup file selection
  const handleBackupFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setBackupFile(files[0]);
    }
  };

  // Download backup function
  const downloadBackup = (backupId: string) => {
    // Create a direct download link to the backup file
    window.location.href = `/api/admin/system/backups/${backupId}/download`;
  };

  // Restore backup mutation
  const restoreBackupMutation = useMutation({
    mutationFn: async (backupId: string) => {
      return apiRequest(`/api/admin/system/backups/${backupId}/restore`, 'POST');
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Backup restored successfully",
      });
      // Refresh all data after restore
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to restore backup",
        variant: "destructive"
      });
    }
  });

  // Delete backup mutation
  const deleteBackupMutation = useMutation({
    mutationFn: async (backupId: string) => {
      return apiRequest(`/api/admin/system/backups/${backupId}`, 'DELETE');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backupInfo'] });
      toast({
        title: "Success",
        description: "Backup deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete backup",
        variant: "destructive"
      });
    }
  });

  // Upload backup mutation
  const uploadBackupMutation = useMutation({
    mutationFn: async (file: File) => {
      setIsUploadingBackup(true);

      const formData = new FormData();
      formData.append('backupFile', file);

      try {
        const response = await fetch('/api/admin/system/backups/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to upload backup');
        }

        return await response.json();
      } finally {
        setIsUploadingBackup(false);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backupInfo'] });
      setUploadBackupDialogOpen(false);
      setBackupFile(null);
      toast({
        title: "Success",
        description: "Backup uploaded and restored successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to upload backup",
        variant: "destructive"
      });
    }
  });

  // Import data mutation
  const importDataMutation = useMutation({
    mutationFn: async ({ endpoint, file }: { endpoint: string; file: File }) => {
      setIsImporting(true);

      const formData = new FormData();
      formData.append('importFile', file);

      try {
        const response = await fetch(`/api/admin/data/${endpoint}`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to import ${endpoint}`);
        }

        return await response.json();
      } finally {
        setIsImporting(false);
      }
    },
    onSuccess: (data, variables) => {
      // Close the appropriate dialog based on the endpoint
      switch (variables.endpoint) {
        case 'products':
          setImportProductsDialogOpen(false);
          break;
        case 'orders':
          setImportOrdersDialogOpen(false);
          break;
        case 'settings':
          setImportSettingsDialogOpen(false);
          break;
        case 'all':
          setImportAllDialogOpen(false);
          break;
      }

      // Reset the import file
      setImportFile(null);

      toast({
        title: "Success",
        description: data.message || "Data imported successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to import data",
        variant: "destructive"
      });
    }
  });

  // Handle import submission
  const handleImport = (endpoint: string) => {
    if (importFile) {
      importDataMutation.mutate({ endpoint, file: importFile });
    }
  };

  // Handle backup upload
  const handleUploadBackup = () => {
    if (backupFile) {
      uploadBackupMutation.mutate(backupFile);
    }
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">System Updates & Backups</CardTitle>
          <CardDescription>
            Manage application updates and data backups
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="updates">
            <TabsList className="mb-6">
              <TabsTrigger value="updates">Updates</TabsTrigger>
              <TabsTrigger value="backups">Backups</TabsTrigger>
              <TabsTrigger value="export">Export/Import</TabsTrigger>
            </TabsList>

            {/* Updates Tab */}
            <TabsContent value="updates">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">System Updates</h3>
                  <div className="space-x-2">
                    <Button variant="outline" onClick={() => setCreateUpdateDialogOpen(true)}>
                      <FileText className="mr-2 h-4 w-4" /> Create Update Package
                    </Button>
                    <Button onClick={() => setUploadDialogOpen(true)}>
                      <Upload className="mr-2 h-4 w-4" /> Upload Update
                    </Button>
                  </div>
                </div>

                {isLoadingUpdateInfo ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle>System Information</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Current Version</p>
                            <p className="text-sm text-muted-foreground">
                              {updateInfo?.systemInfo.version || 'v1.0.0'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Last Updated</p>
                            <p className="text-sm text-muted-foreground">
                              {updateInfo?.lastUpdate || 'Never'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Platform</p>
                            <p className="text-sm text-muted-foreground">
                              {updateInfo?.systemInfo.platform || 'Unknown'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Node Version</p>
                            <p className="text-sm text-muted-foreground">
                              {updateInfo?.systemInfo.nodeVersion || 'Unknown'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* File Tracking Card */}
                    <Card className="mt-6">
                      <CardHeader>
                        <div className="flex justify-between items-center">
                          <CardTitle>File Tracking</CardTitle>
                          <div className="space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => scanFilesMutation.mutate()}
                              disabled={isScanning}
                            >
                              {isScanning ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Scanning...
                                </>
                              ) : (
                                <>
                                  <Search className="mr-2 h-4 w-4" />
                                  Scan Files
                                </>
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => scanFilesMutation.mutate(true)}
                              disabled={isScanning}
                            >
                              {isScanning ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Scanning...
                                </>
                              ) : (
                                <>
                                  <FileSearch className="mr-2 h-4 w-4" />
                                  Force Rescan
                                </>
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => resetFileTrackerMutation.mutate()}
                              disabled={isResetting || isScanning}
                            >
                              {isResetting ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Resetting...
                                </>
                              ) : (
                                <>
                                  <RefreshCw className="mr-2 h-4 w-4" />
                                  Reset Tracker
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Track file changes for creating update packages
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {isLoadingChangedFiles ? (
                          <div className="flex justify-center py-4">
                            <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          </div>
                        ) : (
                          <div>
                            <div className="flex justify-between items-center mb-4">
                              <p className="text-sm font-medium">
                                {changedFilesData?.count || 0} files changed since last scan
                              </p>
                              {changedFilesData?.count ? (
                                <Button
                                  size="sm"
                                  onClick={() => setCreateUpdateDialogOpen(true)}
                                >
                                  Create Update Package
                                </Button>
                              ) : null}
                            </div>

                            {changedFilesData?.count ? (
                              <div className="border rounded-md divide-y max-h-40 overflow-y-auto">
                                {changedFilesData.changedFiles.slice(0, 5).map((file) => (
                                  <div key={file.path} className="flex items-center p-2">
                                    <div className="flex-1">
                                      <div className="text-sm font-medium truncate max-w-md">{file.path}</div>
                                      <div className="text-xs text-muted-foreground">
                                        Modified: {new Date(file.lastModified).toLocaleString()}
                                      </div>
                                    </div>
                                    <Badge variant={
                                      file.status === 'new' ? 'default' :
                                      file.status === 'modified' ? 'secondary' :
                                      'destructive'
                                    }>
                                      {file.status || 'changed'}
                                    </Badge>
                                  </div>
                                ))}
                                {changedFilesData.changedFiles.length > 5 && (
                                  <div className="p-2 text-center text-sm text-muted-foreground">
                                    {changedFilesData.changedFiles.length - 5} more files...
                                  </div>
                                )}
                              </div>
                            ) : (
                              <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertTitle>No changes detected</AlertTitle>
                                <AlertDescription>
                                  Make changes to your files and scan again to detect changes.
                                </AlertDescription>
                              </Alert>
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    <h4 className="text-md font-medium mt-6">Update History</h4>
                    {updateInfo?.updateHistory && updateInfo.updateHistory.length > 0 ? (
                      <div className="space-y-4">
                        {updateInfo.updateHistory.map((update) => (
                          <Card key={update.id}>
                            <CardHeader className="py-3">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center">
                                  <CardTitle className="text-md">{update.version}</CardTitle>
                                  <Badge
                                    className="ml-2"
                                    variant={update.status === 'success' ? 'default' : 'destructive'}
                                  >
                                    {update.status}
                                  </Badge>
                                </div>
                                <span className="text-sm text-muted-foreground">{update.date}</span>
                              </div>
                            </CardHeader>
                            <CardContent className="py-2">
                              <p className="text-sm">{update.details}</p>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No update history available</p>
                    )}
                  </>
                )}
              </div>
            </TabsContent>

            {/* Backups Tab */}
            <TabsContent value="backups">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Data Backups</h3>
                  <div className="space-x-2">
                    <Button variant="outline" onClick={() => setUploadBackupDialogOpen(true)}>
                      <Upload className="mr-2 h-4 w-4" /> Upload Backup
                    </Button>
                    <Button onClick={() => setCreateBackupDialogOpen(true)}>
                      <Database className="mr-2 h-4 w-4" /> Create Backup
                    </Button>
                  </div>
                </div>

                {isLoadingBackupInfo ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle>Backup Information</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Last Backup</p>
                            <p className="text-sm text-muted-foreground">
                              {backupInfo?.lastBackup || 'Never'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Auto Backup</p>
                            <p className="text-sm text-muted-foreground">
                              {backupInfo?.autoBackupEnabled ? 'Enabled' : 'Disabled'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <h4 className="text-md font-medium mt-6">Available Backups</h4>
                    {backupInfo?.backups && backupInfo.backups.length > 0 ? (
                      <div className="space-y-4">
                        {backupInfo.backups.map((backup) => (
                          <Card key={backup.id}>
                            <CardHeader className="py-3">
                              <div className="flex justify-between items-center">
                                <div className="flex items-center">
                                  <CardTitle className="text-md">{backup.name}</CardTitle>
                                  <Badge className="ml-2">
                                    {backup.type}
                                  </Badge>
                                </div>
                                <span className="text-sm text-muted-foreground">{backup.date}</span>
                              </div>
                            </CardHeader>
                            <CardContent className="py-2">
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-muted-foreground">Size: {backup.size}</span>
                                <div className="space-x-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => downloadBackup(backup.id)}
                                  >
                                    <DownloadCloud className="mr-2 h-4 w-4" /> Download
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      if (window.confirm(`Are you sure you want to restore from the backup "${backup.name}"? This will overwrite your current data.`)) {
                                        restoreBackupMutation.mutate(backup.id);
                                      }
                                    }}
                                  >
                                    <RefreshCw className="mr-2 h-4 w-4" /> Restore
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      if (window.confirm(`Are you sure you want to delete the backup "${backup.name}"?`)) {
                                        deleteBackupMutation.mutate(backup.id);
                                      }
                                    }}
                                  >
                                    <Trash className="mr-2 h-4 w-4" /> Delete
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No backups available</p>
                    )}
                  </>
                )}
              </div>
            </TabsContent>

            {/* Export/Import Tab */}
            <TabsContent value="export">
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Data Export & Import</h3>

                <Card>
                  <CardHeader>
                    <CardTitle>Export Data</CardTitle>
                    <CardDescription>
                      Export your application data for backup or migration
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Products</h4>
                          <p className="text-sm text-muted-foreground">Export all product data</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => exportProducts()}>
                          <DownloadCloud className="mr-2 h-4 w-4" /> Export
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Orders & Invoices</h4>
                          <p className="text-sm text-muted-foreground">Export all order and invoice data</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => exportOrders()}>
                          <DownloadCloud className="mr-2 h-4 w-4" /> Export
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Settings</h4>
                          <p className="text-sm text-muted-foreground">Export all application settings</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => exportSettings()}>
                          <DownloadCloud className="mr-2 h-4 w-4" /> Export
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Complete Export</h4>
                          <p className="text-sm text-muted-foreground">Export all application data</p>
                        </div>
                        <Button size="sm" onClick={() => exportAll()}>
                          <DownloadCloud className="mr-2 h-4 w-4" /> Export All
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Import Data</CardTitle>
                    <CardDescription>
                      Import data from previous exports
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Products</h4>
                          <p className="text-sm text-muted-foreground">Import product data</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => setImportProductsDialogOpen(true)}>
                          <Upload className="mr-2 h-4 w-4" /> Import
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Orders & Invoices</h4>
                          <p className="text-sm text-muted-foreground">Import order and invoice data</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => setImportOrdersDialogOpen(true)}>
                          <Upload className="mr-2 h-4 w-4" /> Import
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Settings</h4>
                          <p className="text-sm text-muted-foreground">Import application settings</p>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => setImportSettingsDialogOpen(true)}>
                          <Upload className="mr-2 h-4 w-4" /> Import
                        </Button>
                      </div>
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">Complete Import</h4>
                          <p className="text-sm text-muted-foreground">Import all application data</p>
                        </div>
                        <Button size="sm" onClick={() => setImportAllDialogOpen(true)}>
                          <Upload className="mr-2 h-4 w-4" /> Import All
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upload Update Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload System Update</DialogTitle>
            <DialogDescription>
              Upload a ZIP file containing the system update. The system will be backed up before applying the update.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="update-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Archive className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">ZIP file only</p>
                </div>
                <input
                  id="update-file"
                  type="file"
                  accept=".zip"
                  className="hidden"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </label>
            </div>

            {selectedFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{selectedFile.name}</span> ({Math.round(selectedFile.size / 1024)} KB)
              </div>
            )}

            {isUploading && (
              <div className="space-y-2">
                <Progress value={uploadProgress} className="w-full" />
                <p className="text-xs text-center text-muted-foreground">
                  Uploading and applying update... {uploadProgress}%
                </p>
              </div>
            )}

            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                The system will be backed up automatically before applying the update.
                Make sure the ZIP file contains a valid update package.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setUploadDialogOpen(false)}
              disabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!selectedFile || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Upload & Apply'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Backup Dialog */}
      <Dialog open={createBackupDialogOpen} onOpenChange={setCreateBackupDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Backup</DialogTitle>
            <DialogDescription>
              Create a backup of your application data. This will include all products, orders, invoices, and settings.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="backup-name" className="text-sm font-medium">
                Backup Name
              </label>
              <input
                id="backup-name"
                type="text"
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Weekly Backup - May 2023"
                value={backupName}
                onChange={(e) => setBackupName(e.target.value)}
                disabled={isCreatingBackup}
              />
            </div>

            {isCreatingBackup && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCreateBackupDialogOpen(false)}
              disabled={isCreatingBackup}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateBackup}
              disabled={!backupName.trim() || isCreatingBackup}
            >
              {isCreatingBackup ? 'Creating...' : 'Create Backup'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Update Package Dialog */}
      <Dialog open={createUpdateDialogOpen} onOpenChange={setCreateUpdateDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Create Update Package</DialogTitle>
            <DialogDescription>
              Select files to include in the update package
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-sm font-medium">Version Information</p>
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  className="w-32 p-2 border rounded-md"
                  placeholder="Version (e.g., 1.0.1)"
                  value={updateVersion}
                  onChange={(e) => setUpdateVersion(e.target.value)}
                  disabled={isCreatingPackage}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <p className="text-sm font-medium">Description</p>
              </div>
              <textarea
                className="w-full p-2 border rounded-md"
                placeholder="Description of this update"
                rows={3}
                value={updateDescription}
                onChange={(e) => setUpdateDescription(e.target.value)}
                disabled={isCreatingPackage}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <p className="text-sm font-medium">Changed Files</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleAllFiles}
                  disabled={isCreatingPackage || !changedFilesData?.count}
                >
                  {changedFilesData?.changedFiles.length === selectedFiles.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>

              {isLoadingChangedFiles ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : changedFilesData?.count ? (
                <div className="border rounded-md divide-y max-h-80 overflow-y-auto">
                  {changedFilesData.changedFiles.map((file) => (
                    <div key={file.path} className="flex items-center p-2 hover:bg-gray-50">
                      <Checkbox
                        id={`file-${file.path}`}
                        checked={selectedFiles.includes(file.path)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedFiles([...selectedFiles, file.path]);
                          } else {
                            setSelectedFiles(selectedFiles.filter(p => p !== file.path));
                          }
                        }}
                        disabled={isCreatingPackage}
                      />
                      <label htmlFor={`file-${file.path}`} className="ml-2 flex-1 cursor-pointer">
                        <div className="text-sm font-medium">{file.path}</div>
                        <div className="text-xs text-muted-foreground">
                          Modified: {new Date(file.lastModified).toLocaleString()}
                        </div>
                      </label>
                      <Badge variant={
                        file.status === 'new' ? 'default' :
                        file.status === 'modified' ? 'secondary' :
                        'destructive'
                      }>
                        {file.status || 'changed'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>No changes detected</AlertTitle>
                  <AlertDescription>
                    Make changes to your files and scan again to detect changes.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCreateUpdateDialogOpen(false)}
              disabled={isCreatingPackage}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateUpdatePackage}
              disabled={selectedFiles.length === 0 || !updateVersion.trim() || isCreatingPackage}
            >
              {isCreatingPackage ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Update Package'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Upload Backup Dialog */}
      <Dialog open={uploadBackupDialogOpen} onOpenChange={setUploadBackupDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Backup</DialogTitle>
            <DialogDescription>
              Upload a backup file to restore your application data
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="backup-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Archive className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">ZIP file only</p>
                </div>
                <input
                  id="backup-file"
                  type="file"
                  accept=".zip"
                  className="hidden"
                  onChange={handleBackupFileChange}
                  disabled={isUploadingBackup}
                />
              </label>
            </div>

            {backupFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{backupFile.name}</span> ({Math.round(backupFile.size / 1024)} KB)
              </div>
            )}

            {isUploadingBackup && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Uploading a backup will restore your application data to the state saved in the backup file.
                This will overwrite your current data.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setUploadBackupDialogOpen(false)}
              disabled={isUploadingBackup}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUploadBackup}
              disabled={!backupFile || isUploadingBackup}
            >
              {isUploadingBackup ? 'Uploading...' : 'Upload & Restore'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Products Dialog */}
      <Dialog open={importProductsDialogOpen} onOpenChange={setImportProductsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Products</DialogTitle>
            <DialogDescription>
              Import product data from a JSON file
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="import-products-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Upload className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">JSON file only</p>
                </div>
                <input
                  id="import-products-file"
                  type="file"
                  accept=".json,application/json"
                  className="hidden"
                  onChange={handleImportFileChange}
                  disabled={isImporting}
                />
              </label>
            </div>

            {importFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{importFile.name}</span> ({Math.round(importFile.size / 1024)} KB)
              </div>
            )}

            {isImporting && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Importing products will not delete existing products. It will add new products and update existing ones.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setImportProductsDialogOpen(false);
                setImportFile(null);
              }}
              disabled={isImporting}
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleImport('products')}
              disabled={!importFile || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import Products'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Orders Dialog */}
      <Dialog open={importOrdersDialogOpen} onOpenChange={setImportOrdersDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Orders & Invoices</DialogTitle>
            <DialogDescription>
              Import order and invoice data from a JSON file
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="import-orders-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Upload className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">JSON file only</p>
                </div>
                <input
                  id="import-orders-file"
                  type="file"
                  accept=".json,application/json"
                  className="hidden"
                  onChange={handleImportFileChange}
                  disabled={isImporting}
                />
              </label>
            </div>

            {importFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{importFile.name}</span> ({Math.round(importFile.size / 1024)} KB)
              </div>
            )}

            {isImporting && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Importing orders will not delete existing orders. It will add new orders and update existing ones.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setImportOrdersDialogOpen(false);
                setImportFile(null);
              }}
              disabled={isImporting}
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleImport('orders')}
              disabled={!importFile || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import Orders'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Settings Dialog */}
      <Dialog open={importSettingsDialogOpen} onOpenChange={setImportSettingsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Settings</DialogTitle>
            <DialogDescription>
              Import application settings from a JSON file
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="import-settings-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Upload className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">JSON file only</p>
                </div>
                <input
                  id="import-settings-file"
                  type="file"
                  accept=".json,application/json"
                  className="hidden"
                  onChange={handleImportFileChange}
                  disabled={isImporting}
                />
              </label>
            </div>

            {importFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{importFile.name}</span> ({Math.round(importFile.size / 1024)} KB)
              </div>
            )}

            {isImporting && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Alert>
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                Importing settings will overwrite your existing settings. Make sure to back up your settings first.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setImportSettingsDialogOpen(false);
                setImportFile(null);
              }}
              disabled={isImporting}
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleImport('settings')}
              disabled={!importFile || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import Settings'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import All Dialog */}
      <Dialog open={importAllDialogOpen} onOpenChange={setImportAllDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import All Data</DialogTitle>
            <DialogDescription>
              Import all application data from a JSON file
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="import-all-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <Upload className="w-10 h-10 mb-3 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">JSON file only</p>
                </div>
                <input
                  id="import-all-file"
                  type="file"
                  accept=".json,application/json"
                  className="hidden"
                  onChange={handleImportFileChange}
                  disabled={isImporting}
                />
              </label>
            </div>

            {importFile && (
              <div className="text-sm">
                Selected file: <span className="font-medium">{importFile.name}</span> ({Math.round(importFile.size / 1024)} KB)
              </div>
            )}

            {isImporting && (
              <div className="flex justify-center py-2">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            )}

            <Alert variant="destructive">
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Importing all data will overwrite your existing data. Make sure to back up your data first.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setImportAllDialogOpen(false);
                setImportFile(null);
              }}
              disabled={isImporting}
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleImport('all')}
              disabled={!importFile || isImporting}
            >
              {isImporting ? 'Importing...' : 'Import All Data'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
