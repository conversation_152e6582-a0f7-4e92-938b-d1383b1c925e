import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';

export const allowedUsernamesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Schema for creating a new allowed username
const createAllowedUsernameSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  notes: z.string().optional()
});

// Schema for bulk import of usernames
const bulkImportSchema = z.object({
  usernames: z.string().min(1, 'Usernames are required')
});

// Get all allowed usernames
allowedUsernamesRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const usernames = await storage.getAllowedUsernames();
    res.json(usernames);
  } catch (error) {
    console.error('Error fetching allowed usernames:', error);
    res.status(500).json({ message: 'Failed to fetch allowed usernames' });
  }
});

// Create a new allowed username
allowedUsernamesRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = createAllowedUsernameSchema.parse(req.body);
    
    // Check if username already exists
    const usernames = await storage.getAllowedUsernames();
    const exists = usernames.some(u => u.username.toLowerCase() === validatedData.username.toLowerCase());
    
    if (exists) {
      return res.status(400).json({ message: 'Username already exists' });
    }
    
    const username = await storage.createAllowedUsername({
      ...validatedData,
      createdAt: new Date().toISOString()
    });
    
    res.status(201).json(username);
  } catch (error) {
    console.error('Error creating allowed username:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to create allowed username' });
  }
});

// Bulk import usernames
allowedUsernamesRouter.post('/bulk', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = bulkImportSchema.parse(req.body);
    
    // Split the usernames by newline, comma, or space
    const usernameList = validatedData.usernames
      .split(/[\n,\s]+/)
      .map(username => username.trim())
      .filter(username => username.length > 0);
    
    if (usernameList.length === 0) {
      return res.status(400).json({ message: 'No valid usernames provided' });
    }
    
    const result = await storage.bulkCreateAllowedUsernames(usernameList);
    
    res.status(201).json({
      message: `Successfully imported ${result.success} usernames. ${result.failed} failed due to duplicates or invalid format.`,
      success: result.success,
      failed: result.failed
    });
  } catch (error) {
    console.error('Error bulk importing usernames:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to import usernames' });
  }
});

// Delete an allowed username
allowedUsernamesRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }
    
    const username = await storage.getAllowedUsername(id);
    
    if (!username) {
      return res.status(404).json({ message: 'Username not found' });
    }
    
    const deleted = await storage.deleteAllowedUsername(id);
    
    if (deleted) {
      res.json({ message: 'Username deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete username' });
    }
  } catch (error) {
    console.error('Error deleting allowed username:', error);
    res.status(500).json({ message: 'Failed to delete username' });
  }
});

// Check if a username is allowed
allowedUsernamesRouter.get('/check/:username', async (req: Request, res: Response) => {
  try {
    const { username } = req.params;
    const isAllowed = await storage.isUsernameAllowed(username);
    
    res.json({ isAllowed });
  } catch (error) {
    console.error('Error checking username:', error);
    res.status(500).json({ message: 'Failed to check username' });
  }
});
