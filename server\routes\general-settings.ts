import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { getGeneralConfig, updateGeneralConfig, GeneralConfig } from '../general-config';
import { getHomepageConfig, updateHomepageConfig } from '../homepage-config';

export const generalSettingsRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
  }
}

// Admin authentication middleware (same as in admin.ts)
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session for general settings:', req.session);

  // Check if user is authenticated and has completed 2FA if required
  if (req.session.isAdmin) {
    // If 2FA is required but not verified, deny access
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified for general settings:', true);
    next();
  } else {
    console.log('Admin session verified for general settings:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Define the validation schema for general settings
const generalSettingsSchema = z.object({
  siteName: z.string().min(1, "Site name is required"),
  siteDescription: z.string().optional().default(""),
  logoUrl: z.string().optional().default(""),
  faviconUrl: z.string().optional().default(""),
  primaryColor: z.string().optional().default(""),
  secondaryColor: z.string().optional().default(""),
  footerText: z.string().optional().default(""),
  enableCheckout: z.boolean().default(true),
  enableCustomCheckout: z.boolean().default(true),
  enableTestMode: z.boolean().default(true),
  defaultTestCustomer: z.object({
    enabled: z.boolean().default(true),
    name: z.string().default(""),
    email: z.string().default("")
  }),
  emailDomainRestriction: z.object({
    enabled: z.boolean().default(true),
    allowedDomains: z.string().optional().default("")
  })
});

// Get general settings
generalSettingsRouter.get('/', isAdmin, async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    res.json(settings);
  } catch (error) {
    console.error('Error fetching general settings:', error);
    res.status(500).json({ message: 'Failed to fetch general settings' });
  }
});

// Update general settings
generalSettingsRouter.put('/', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/general-settings called with body:', JSON.stringify(req.body, null, 2));

    // Validate the request body
    const validatedData = generalSettingsSchema.parse(req.body);
    console.log('Validation successful, validated data:', JSON.stringify(validatedData, null, 2));

    // Update the settings
    const updatedSettings = updateGeneralConfig(validatedData);
    console.log('Settings updated successfully:', JSON.stringify(updatedSettings, null, 2));

    // Sync with homepage configuration if site name or description changed
    if (validatedData.siteName || validatedData.siteDescription) {
      try {
        const homepageConfig = getHomepageConfig();

        // Update SEO settings in homepage config
        const updatedHomepageConfig = {
          ...homepageConfig,
          seo: {
            ...homepageConfig.seo,
            title: validatedData.siteName || homepageConfig.seo.title,
            description: validatedData.siteDescription || homepageConfig.seo.description,
            ogTitle: validatedData.siteName || homepageConfig.seo.ogTitle,
            ogDescription: validatedData.siteDescription || homepageConfig.seo.ogDescription,
            twitterTitle: validatedData.siteName || homepageConfig.seo.twitterTitle,
            twitterDescription: validatedData.siteDescription || homepageConfig.seo.twitterDescription,
          },
          theme: {
            ...homepageConfig.theme,
            primaryColor: validatedData.primaryColor || homepageConfig.theme.primaryColor,
            secondaryColor: validatedData.secondaryColor || homepageConfig.theme.secondaryColor,
          },
          lastUpdated: new Date().toISOString(),
          version: homepageConfig.version + 1
        };

        updateHomepageConfig(updatedHomepageConfig);
        console.log('Homepage configuration synced with general settings');
      } catch (homepageError) {
        console.error('Error syncing homepage configuration:', homepageError);
        // Don't fail the general settings update if homepage sync fails
      }
    }

    res.json(updatedSettings);
  } catch (error) {
    console.error('Error updating general settings:', error);

    if (error instanceof z.ZodError) {
      console.error('Validation errors:', error.errors);
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update general settings' });
  }
});

// Get allowed email domains
generalSettingsRouter.get('/allowed-email-domains', async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    const { enabled, allowedDomains } = settings.emailDomainRestriction;

    if (!enabled || !allowedDomains) {
      return res.json({ enabled, domains: [] });
    }

    const domains = allowedDomains
      .split(',')
      .map(domain => domain.trim())
      .filter(domain => domain.length > 0);

    res.json({ enabled, domains });
  } catch (error) {
    console.error('Error fetching allowed email domains:', error);
    res.status(500).json({ message: 'Failed to fetch allowed email domains' });
  }
});

// Get default test customer
generalSettingsRouter.get('/default-test-customer', async (req: Request, res: Response) => {
  try {
    const settings = getGeneralConfig();
    const { defaultTestCustomer } = settings;

    res.json(defaultTestCustomer);
  } catch (error) {
    console.error('Error fetching default test customer:', error);
    res.status(500).json({ message: 'Failed to fetch default test customer' });
  }
});
