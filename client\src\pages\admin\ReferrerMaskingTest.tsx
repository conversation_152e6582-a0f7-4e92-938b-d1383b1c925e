import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Copy, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';

export default function ReferrerMaskingTest() {
  const { toast } = useToast();
  const [checkoutSlug, setCheckoutSlug] = useState('default-trial-checkout-uzJSVY');
  const [fakeReferrer, setFakeReferrer] = useState('https://facebook.com');
  const [delay, setDelay] = useState(2000);

  const generateMaskedUrl = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/api/redirect/generate?slug=${checkoutSlug}&referrer=${encodeURIComponent(fakeReferrer)}&delay=${delay}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied!',
      description: 'URL copied to clipboard',
    });
  };

  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Referrer Masking Test</h1>
          <p className="text-muted-foreground">
            Test the referrer masking functionality with different settings
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
              <CardDescription>
                Configure the referrer masking settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="checkout-slug">Checkout Page Slug</Label>
                <Input
                  id="checkout-slug"
                  value={checkoutSlug}
                  onChange={(e) => setCheckoutSlug(e.target.value)}
                  placeholder="default-trial-checkout-uzJSVY"
                />
              </div>

              <div>
                <Label htmlFor="fake-referrer">Fake Referrer Domain</Label>
                <Select value={fakeReferrer} onValueChange={setFakeReferrer}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="https://facebook.com">Facebook</SelectItem>
                    <SelectItem value="https://google.com">Google</SelectItem>
                    <SelectItem value="https://twitter.com">Twitter</SelectItem>
                    <SelectItem value="https://instagram.com">Instagram</SelectItem>
                    <SelectItem value="https://youtube.com">YouTube</SelectItem>
                    <SelectItem value="https://linkedin.com">LinkedIn</SelectItem>
                    <SelectItem value="https://reddit.com">Reddit</SelectItem>
                    <SelectItem value="https://tiktok.com">TikTok</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="delay">Redirect Delay (ms)</Label>
                <Input
                  id="delay"
                  type="number"
                  min="0"
                  max="10000"
                  step="500"
                  value={delay}
                  onChange={(e) => setDelay(parseInt(e.target.value) || 0)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Generated URLs */}
          <Card>
            <CardHeader>
              <CardTitle>Generated URLs</CardTitle>
              <CardDescription>
                Compare the original and masked URLs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Original Checkout URL</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    readOnly
                    value={`${window.location.origin}/checkout/${checkoutSlug}`}
                    className="font-mono text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(`${window.location.origin}/checkout/${checkoutSlug}`)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openInNewTab(`${window.location.origin}/checkout/${checkoutSlug}`)}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label>Masked Checkout URL</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    readOnly
                    value={generateMaskedUrl()}
                    className="font-mono text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generateMaskedUrl())}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openInNewTab(generateMaskedUrl())}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">How it works:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>1. User clicks the masked URL</li>
                  <li>2. Shows loading page for {delay}ms</li>
                  <li>3. Redirects with {fakeReferrer} as referrer</li>
                  <li>4. Payment gateway sees fake referrer</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p>
                <strong>1. Test the Original URL:</strong> Click the original checkout URL to see the normal behavior.
              </p>
              <p>
                <strong>2. Test the Masked URL:</strong> Click the masked checkout URL to see the referrer masking in action.
              </p>
              <p>
                <strong>3. Check Developer Tools:</strong> Open browser dev tools → Network tab → check the referrer header in requests.
              </p>
              <p>
                <strong>4. Verify Referrer:</strong> The masked URL should show {fakeReferrer} as the referrer instead of the actual source.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
