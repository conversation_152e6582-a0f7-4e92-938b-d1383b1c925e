import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ProductCardProps } from '@/lib/types';

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect }) => {
  return (
    <Card className="overflow-hidden shadow-sm hover:shadow-md transition-all border cursor-pointer" onClick={() => onSelect(product)}>
      <img 
        src={product.imageUrl} 
        alt={product.name} 
        className="w-full h-48 object-cover" 
      />
      <CardContent className="p-4">
        <h3 className="font-semibold text-xl mb-2">{product.name}</h3>
        <p className="text-muted-foreground mb-3 text-sm line-clamp-2">{product.description}</p>
        <div className="flex justify-between items-center">
          <span className="font-bold text-lg">${parseFloat(product.price.toString()).toFixed(2)}</span>
          <Button 
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
            onClick={(e) => {
              e.stopPropagation();
              onSelect(product);
            }}
          >
            Select
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
