import React from 'react';
import { type PageSection, type FeaturesSection as FeaturesSectionType, type ThemeSettings } from '@/api/homepage';

interface FeaturesSectionProps {
  section: PageSection;
  theme?: ThemeSettings;
}

const FeaturesSection: React.FC<FeaturesSectionProps> = ({ section, theme }) => {
  const content = section.content as FeaturesSectionType;
  const enabledFeatures = content.features.filter(feature => feature.enabled);

  // Get grid columns class based on columns setting
  const getGridClass = () => {
    const cols = Math.min(content.columns || 3, 6);
    const colsMap = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    };
    return colsMap[cols as keyof typeof colsMap] || colsMap[3];
  };

  // Render feature item
  const renderFeature = (feature: any, index: number) => (
    <div 
      key={feature.id}
      className="text-center p-6 rounded-lg transition-all duration-300 hover:shadow-lg hover:scale-105"
      style={{
        animationDelay: `${index * 0.1}s`
      }}
    >
      {/* Icon */}
      <div className="mb-4">
        {feature.icon ? (
          <div 
            className="text-4xl md:text-5xl mb-2"
            role="img"
            aria-label={feature.title}
          >
            {feature.icon}
          </div>
        ) : (
          <div 
            className="w-16 h-16 mx-auto rounded-full flex items-center justify-center text-white text-2xl font-bold"
            style={{ backgroundColor: theme?.primaryColor || '#0070ba' }}
          >
            {feature.title.charAt(0)}
          </div>
        )}
      </div>

      {/* Title */}
      <h3 
        className="text-xl font-semibold mb-3"
        style={{ color: theme?.textColor || '#1e293b' }}
      >
        {feature.title}
      </h3>

      {/* Description */}
      <p 
        className="text-gray-600 leading-relaxed"
        style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
      >
        {feature.description}
      </p>
    </div>
  );

  // Render based on layout
  const renderFeatures = () => {
    switch (content.layout) {
      case 'list':
        return (
          <div className="space-y-8">
            {enabledFeatures.map((feature, index) => (
              <div 
                key={feature.id}
                className="flex flex-col md:flex-row items-center text-center md:text-left p-6 rounded-lg hover:shadow-lg transition-all duration-300"
                style={{
                  animationDelay: `${index * 0.1}s`
                }}
              >
                {/* Icon */}
                <div className="mb-4 md:mb-0 md:mr-6 flex-shrink-0">
                  {feature.icon ? (
                    <div className="text-4xl" role="img" aria-label={feature.title}>
                      {feature.icon}
                    </div>
                  ) : (
                    <div 
                      className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold"
                      style={{ backgroundColor: theme?.primaryColor || '#0070ba' }}
                    >
                      {feature.title.charAt(0)}
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="flex-grow">
                  <h3 
                    className="text-xl font-semibold mb-2"
                    style={{ color: theme?.textColor || '#1e293b' }}
                  >
                    {feature.title}
                  </h3>
                  <p 
                    className="text-gray-600"
                    style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
                  >
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        );

      case 'carousel':
        // TODO: Implement carousel layout
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Carousel layout coming soon...</p>
            <div className={`grid ${getGridClass()} gap-8 mt-8`}>
              {enabledFeatures.map(renderFeature)}
            </div>
          </div>
        );

      case 'grid':
      default:
        return (
          <div className={`grid ${getGridClass()} gap-8`}>
            {enabledFeatures.map(renderFeature)}
          </div>
        );
    }
  };

  if (enabledFeatures.length === 0) {
    return null;
  }

  return (
    <section 
      className="py-16 px-4"
      style={{ backgroundColor: theme?.backgroundColor || '#ffffff' }}
      id="features"
    >
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-12">
          {/* Title */}
          <h2 
            className="text-3xl md:text-4xl font-bold mb-4"
            style={{ color: theme?.textColor || '#1e293b' }}
          >
            {content.title}
          </h2>

          {/* Subtitle */}
          {content.subtitle && (
            <p 
              className="text-lg md:text-xl max-w-3xl mx-auto"
              style={{ color: theme?.textColor ? `${theme.textColor}80` : '#64748b' }}
            >
              {content.subtitle}
            </p>
          )}

          {/* Decorative line */}
          <div className="mt-6 flex justify-center">
            <div 
              className="w-20 h-1 rounded-full"
              style={{ backgroundColor: theme?.primaryColor || '#0070ba' }}
            ></div>
          </div>
        </div>

        {/* Features */}
        <div className="animate-fade-in">
          {renderFeatures()}
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div 
          className="absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-5"
          style={{ backgroundColor: theme?.primaryColor || '#0070ba' }}
        ></div>
        <div 
          className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full opacity-5"
          style={{ backgroundColor: theme?.secondaryColor || '#003087' }}
        ></div>
      </div>
    </section>
  );
};

export default FeaturesSection;
