import { Request, Response, NextFunction } from 'express';

// Middleware to check if user is authenticated as admin
export function isAdmin(req: Request, res: Response, next: NextFunction) {
  // Check if session exists and user is admin
  if (req.session && req.session.isAdmin) {
    // Log session info for debugging
    console.log('Checking session:', { 
      id: req.session.id, 
      isAdmin: req.session.isAdmin 
    });
    
    // User is authenticated as admin, proceed
    next();
  } else {
    // User is not authenticated as admin
    res.status(401).json({ message: 'Unauthorized' });
  }
}
