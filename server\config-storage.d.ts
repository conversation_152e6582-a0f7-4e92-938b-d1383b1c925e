// Type declarations for config-storage.ts

// Email provider interfaces
interface EmailCredentials {
  fromEmail: string;
  fromName: string;
  host: string;
  port: string;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailProvider {
  id: string;
  name: string;
  active: boolean;
  credentials: EmailCredentials;
}

interface EmailConfig {
  providers: EmailProvider[];
}

// Payment provider interfaces
interface PayPalConfig {
  clientId: string;
  clientSecret: string;
  mode: string;
  webhookId: string;
}

interface PaymentProvider {
  id: string;
  name: string;
  active: boolean;
  config: PayPalConfig;
}

interface PaymentConfig {
  providers: PaymentProvider[];
}

interface ConfigStorage {
  email: EmailConfig;
  payment: PaymentConfig;
}

export const configStorage: ConfigStorage;
export function getEmailConfig(): EmailConfig;
export function getPaymentConfig(): PaymentConfig;