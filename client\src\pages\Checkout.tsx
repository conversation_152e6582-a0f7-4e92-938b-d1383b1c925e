import React from 'react';
import { useLocation, useRoute } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Product } from '@shared/schema';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CheckoutForm from '@/components/CheckoutForm';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';

const Checkout: React.FC = () => {
  const [, params] = useRoute('/checkout/product/:id');
  const [, setLocation] = useLocation();
  const productId = params?.id ? parseInt(params.id) : null;

  // Redirect if no product ID
  if (!productId) {
    setLocation('/');
    return null;
  }

  // Fetch product
  const { data: product, isLoading, error } = useQuery<Product>({
    queryKey: [`/api/products/${productId}`],
    retry: 2, // Retry up to 2 times if the request fails
    onError: (err) => {
      console.error('Failed to fetch product:', err);
    }
  });

  // Handle back to products
  const handleBackToProducts = () => {
    setLocation('/');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />

        <main className="flex-grow py-12 px-4">
          <div className="container mx-auto max-w-3xl">
            <Skeleton className="h-10 w-36 mb-6" />
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-24 w-full mb-4" />
                <Skeleton className="h-8 w-72 mb-2" />
                <Skeleton className="h-10 w-full mb-4" />
                <Skeleton className="h-10 w-full mb-4" />
                <Skeleton className="h-14 w-full" />
              </CardContent>
            </Card>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />

        <main className="flex-grow py-12 px-4 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 flex flex-col items-center">
              <AlertCircle className="h-12 w-12 text-destructive mb-4" />
              <h2 className="text-xl font-semibold mb-2">Product Not Found</h2>
              <p className="text-muted-foreground text-center mb-4">
                We couldn't find the product you're looking for.
              </p>
              <button
                className="text-primary hover:underline"
                onClick={handleBackToProducts}
              >
                Return to products
              </button>
            </CardContent>
          </Card>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow py-12 px-4">
        <CheckoutForm
          product={product}
          onBack={handleBackToProducts}
        />
      </main>

      <Footer />
    </div>
  );
};

export default Checkout;
