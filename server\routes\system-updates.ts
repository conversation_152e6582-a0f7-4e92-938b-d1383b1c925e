import express, { Request, Response } from 'express';
import { isAdmin } from '../middleware/auth';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import AdmZip from 'adm-zip';
import { storage, MemStorage } from '../storage';
import { FileTracker, FileRecord } from '../file-tracker';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Define extended storage interface with save methods
interface ExtendedStorage extends MemStorage {
  saveProducts?: (products: any[]) => Promise<void>;
  saveInvoices?: (invoices: any[]) => Promise<void>;
  saveCustomCheckoutPages?: (pages: any[]) => Promise<void>;
  saveAllowedEmails?: (emails: any[]) => Promise<void>;
  saveEmailTemplates?: (templates: any[]) => Promise<void>;
  savePaypalButtons?: (buttons: any[]) => Promise<void>;
}

// Cast storage to ExtendedStorage
const extendedStorage = storage as ExtendedStorage;

// Get the current file's directory
let __dirname: string;
try {
  const __filename = fileURLToPath(import.meta.url);
  __dirname = dirname(__filename);
} catch (error) {
  // Fallback for bundled environments
  __dirname = process.cwd();
  console.log(`Using fallback directory path: ${__dirname}`);
}

const systemUpdatesRouter = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadDir = path.join(__dirname, '../uploads');

      // Create uploads directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, 'update-' + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    // Only accept zip files
    if (file.mimetype === 'application/zip' || path.extname(file.originalname).toLowerCase() === '.zip') {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP files are allowed'));
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB max file size
  }
});

// Mock data for demo purposes
// In a real implementation, this would be stored in a database
let updateHistory = [
  {
    id: '1',
    date: '2023-05-15 14:30:22',
    version: 'v1.1.0',
    status: 'success',
    details: 'Initial system setup'
  }
];

// Define backup type
type BackupType = 'manual' | 'auto' | 'pre-update' | 'pre-restore';

// Define backup interface
interface Backup {
  id: string;
  date: string;
  size: string;
  type: BackupType;
  name: string;
}

// Initialize backups array
let backups: Array<Backup> = [];

// Function to initialize backups from the filesystem
async function initializeBackupsFromFiles() {
  try {
    const backupDir = path.join(__dirname, '../backups');

    // Create backups directory if it doesn't exist
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      console.log(`Created backups directory: ${backupDir}`);
      return; // No files to scan yet
    }

    // Read all files in the backups directory
    const files = fs.readdirSync(backupDir);

    // Filter for backup zip files
    const backupFiles = files.filter(file =>
      file.startsWith('backup-') && file.endsWith('.zip')
    );

    console.log(`Found ${backupFiles.length} backup files in ${backupDir}`);

    // Create a new array for backups
    const newBackups: Backup[] = [];

    // Process each backup file
    for (const file of backupFiles) {
      try {
        // Extract the backup ID from the filename (backup-{id}.zip)
        const id = file.substring(7, file.length - 4);

        // Get file stats
        const stats = fs.statSync(path.join(backupDir, file));
        // Format size in KB or MB as appropriate
        let sizeStr;
        if (stats.size < 1024) {
          sizeStr = `${stats.size} bytes`;
        } else if (stats.size < 1024 * 1024) {
          sizeStr = `${(stats.size / 1024).toFixed(1)} KB`;
        } else {
          sizeStr = `${(stats.size / (1024 * 1024)).toFixed(1)} MB`;
        }

        const modifiedDate = new Date(stats.mtime).toISOString().replace('T', ' ').substring(0, 19);

        // Try to extract backup name from the zip file
        let backupName = 'Backup ' + id.substring(0, 8);
        try {
          const zip = new AdmZip(path.join(backupDir, file));
          const readmeEntry = zip.getEntry('README.txt');

          if (readmeEntry) {
            const readmeContent = zip.readAsText(readmeEntry);
            const nameMatch = readmeContent.match(/Backup: (.*)/);
            if (nameMatch && nameMatch[1]) {
              backupName = nameMatch[1];
            }
          }
        } catch (zipError) {
          console.error(`Error reading backup zip file ${file}:`, zipError);
        }

        // Check if this backup already exists in the backups array
        const existingBackup = backups.find(b => b.id === id);

        if (existingBackup) {
          // Update the existing backup with the current file info
          newBackups.push({
            ...existingBackup,
            date: modifiedDate,
            size: sizeStr,
            name: existingBackup.name || backupName
          });
          console.log(`Updated existing backup: ${file}, ID: ${id}, Size: ${sizeStr}`);
        } else {
          // Add new backup to array
          newBackups.push({
            id,
            date: modifiedDate,
            size: sizeStr,
            type: 'manual' as BackupType,
            name: backupName
          });
          console.log(`Added new backup from file: ${file}, ID: ${id}, Name: ${backupName}, Size: ${sizeStr}`);
        }
      } catch (fileError) {
        console.error(`Error processing backup file ${file}:`, fileError);
      }
    }

    // Replace the backups array with the new one
    backups = newBackups;

    // Sort backups by date (newest first)
    backups.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    console.log(`Initialized ${backups.length} backups from filesystem`);
  } catch (error) {
    console.error('Error initializing backups from filesystem:', error);

    // Only use fallback if no backups exist
    if (backups.length === 0) {
      backups = [
        {
          id: '1',
          date: '2023-05-15 14:00:00',
          size: '2.4 MB',
          type: 'manual',
          name: 'Initial Backup'
        }
      ];
    }
  }
}

// Initialize backups when the server starts
initializeBackupsFromFiles();

// Get system update information
systemUpdatesRouter.get('/updates', isAdmin, async (req: Request, res: Response) => {
  try {
    // In a real implementation, this would fetch data from a database
    // and gather actual system information
    const updateInfo = {
      lastUpdate: updateHistory.length > 0 ? updateHistory[0].date : null,
      updateHistory: updateHistory,
      systemInfo: {
        version: 'v1.0.0',
        nodeVersion: process.version,
        platform: process.platform,
        uptime: Math.floor(process.uptime())
      }
    };

    res.json(updateInfo);
  } catch (error) {
    console.error('Error fetching update information:', error);
    res.status(500).json({ message: 'Failed to fetch update information' });
  }
});

// Upload and apply system update
systemUpdatesRouter.post('/upload', isAdmin, upload.single('updateFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Create a backup before applying the update
    const backupId = await createBackup('pre-update');

    // Process the update file
    const updateResult = await processUpdateFile(uploadedFile.path);

    // Add to update history
    const updateRecord = {
      id: uuidv4(),
      date: new Date().toISOString().replace('T', ' ').substring(0, 19),
      version: updateResult.version || 'Unknown',
      status: updateResult.success ? 'success' : 'failed',
      details: updateResult.message
    };

    // In a real implementation, this would be stored in a database
    updateHistory.unshift(updateRecord);

    res.json({
      success: updateResult.success,
      message: updateResult.message,
      updateRecord
    });
  } catch (error) {
    console.error('Error uploading update:', error);

    // Add to update history as failed
    const updateRecord = {
      id: uuidv4(),
      date: new Date().toISOString().replace('T', ' ').substring(0, 19),
      version: 'Unknown',
      status: 'failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    };

    // In a real implementation, this would be stored in a database
    updateHistory.unshift(updateRecord);

    res.status(500).json({
      message: 'Failed to upload and apply update',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get backup information
systemUpdatesRouter.get('/backups', isAdmin, async (req: Request, res: Response) => {
  try {
    // In a real implementation, this would fetch data from a database
    const backupInfo = {
      lastBackup: backups.length > 0 ? backups[0].date : null,
      backups: backups,
      autoBackupEnabled: true
    };

    res.json(backupInfo);
  } catch (error) {
    console.error('Error fetching backup information:', error);
    res.status(500).json({ message: 'Failed to fetch backup information' });
  }
});

// Create a new backup
systemUpdatesRouter.post('/backups', isAdmin, async (req: Request, res: Response) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Backup name is required' });
    }

    const backupId = await createBackup('manual', name);

    res.json({
      success: true,
      message: 'Backup created successfully',
      backupId
    });
  } catch (error) {
    console.error('Error creating backup:', error);
    res.status(500).json({
      message: 'Failed to create backup',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Download a backup
systemUpdatesRouter.get('/backups/:id/download', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Find the backup
    const backup = backups.find(b => b.id === id);

    if (!backup) {
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Get the backup file path
    const backupDir = path.join(__dirname, '../backups');
    const backupFilePath = path.join(backupDir, `backup-${id}.zip`);

    // Check if the backup file exists
    if (!fs.existsSync(backupFilePath)) {
      // If the file doesn't exist, create it on-demand
      await createBackupFile(id, backup.name);
    }

    // Send the backup file for download
    res.download(backupFilePath, `backup-${backup.name.replace(/\s+/g, '-')}-${backup.date.replace(/\s+/g, '-')}.zip`, (err) => {
      if (err) {
        console.error('Error sending backup file:', err);
      }
    });
  } catch (error) {
    console.error('Error downloading backup:', error);
    res.status(500).json({
      message: 'Failed to download backup',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete a backup
systemUpdatesRouter.delete('/backups/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    console.log(`Attempting to delete backup with ID: ${id}`);

    // Find the backup
    const backupIndex = backups.findIndex(b => b.id === id);

    if (backupIndex === -1) {
      console.error(`Backup with ID ${id} not found in records`);
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Get the backup file path
    const backupDir = path.join(__dirname, '../backups');
    const backupFilePath = path.join(backupDir, `backup-${id}.zip`);

    // Check if the backup file exists and delete it
    if (fs.existsSync(backupFilePath)) {
      fs.unlinkSync(backupFilePath);
      console.log(`Deleted backup file: ${backupFilePath}`);
    } else {
      console.warn(`Backup file not found at: ${backupFilePath}, but proceeding with record deletion`);
    }

    // Remove the backup from the list
    const deletedBackup = backups.splice(backupIndex, 1)[0];
    console.log(`Removed backup from list: ${deletedBackup.name}`);

    res.json({
      success: true,
      message: 'Backup deleted successfully',
      backupId: id
    });
  } catch (error) {
    console.error('Error deleting backup:', error);
    res.status(500).json({
      message: 'Failed to delete backup',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Restore from a backup
systemUpdatesRouter.post('/backups/:id/restore', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    console.log(`Attempting to restore backup with ID: ${id}`);

    // Find the backup
    const backup = backups.find(b => b.id === id);

    if (!backup) {
      console.error(`Backup with ID ${id} not found in records`);
      return res.status(404).json({ message: 'Backup not found in records' });
    }

    // Get the backup file path
    const backupDir = path.join(__dirname, '../backups');
    const backupFilePath = path.join(backupDir, `backup-${id}.zip`);

    console.log(`Looking for backup file at: ${backupFilePath}`);

    // Ensure the backups directory exists
    if (!fs.existsSync(backupDir)) {
      console.error(`Backups directory does not exist: ${backupDir}`);
      fs.mkdirSync(backupDir, { recursive: true });
      console.log(`Created backups directory: ${backupDir}`);
    }

    // Check if the backup file exists
    if (!fs.existsSync(backupFilePath)) {
      console.error(`Backup file not found at: ${backupFilePath}`);

      // Check if we can create the backup file on-demand
      try {
        console.log(`Attempting to create backup file on-demand for ID: ${id}`);
        await createBackupFile(id, backup.name);
        console.log(`Successfully created backup file on-demand for ID: ${id}`);
      } catch (createError) {
        console.error(`Failed to create backup file on-demand:`, createError);
        return res.status(404).json({
          message: 'Backup file not found and could not be created on-demand',
          details: createError instanceof Error ? createError.message : 'Unknown error'
        });
      }

      // Check again if the file exists after creation attempt
      if (!fs.existsSync(backupFilePath)) {
        console.error(`Backup file still not found after creation attempt: ${backupFilePath}`);
        return res.status(404).json({ message: 'Backup file not found after creation attempt' });
      }
    }

    // Restore from the backup file
    await restoreFromBackupFile(backupFilePath);

    res.json({
      success: true,
      message: 'Backup restored successfully',
      backupId: id
    });
  } catch (error) {
    console.error('Error restoring backup:', error);
    res.status(500).json({
      message: 'Failed to restore backup',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Upload and restore from a backup
systemUpdatesRouter.post('/backups/upload', isAdmin, upload.single('backupFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Create a backup before restoring from the uploaded file
    const backupId = await createBackup('pre-restore', 'Pre-restore Backup');

    // Restore from the uploaded backup file
    await restoreFromBackupFile(uploadedFile.path);

    // Clean up the uploaded file
    fs.unlinkSync(uploadedFile.path);

    res.json({
      success: true,
      message: 'Backup uploaded and restored successfully'
    });
  } catch (error) {
    console.error('Error uploading and restoring backup:', error);
    res.status(500).json({
      message: 'Failed to upload and restore backup',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to create a backup
async function createBackup(type: 'manual' | 'auto' | 'pre-update' | 'pre-restore', name?: string): Promise<string> {
  try {
    console.log(`Creating new backup of type: ${type}, name: ${name || 'not specified'}`);

    // Create a unique ID for the backup
    const backupId = uuidv4();
    const backupName = name || `${type.charAt(0).toUpperCase() + type.slice(1)} Backup - ${new Date().toLocaleDateString()}`;
    const backupDate = new Date().toISOString().replace('T', ' ').substring(0, 19);

    console.log(`Generated backup ID: ${backupId}`);

    // Create the backup file
    await createBackupFile(backupId, backupName);

    // Get the file size
    const backupDir = path.join(__dirname, '../backups');
    const backupFilePath = path.join(backupDir, `backup-${backupId}.zip`);

    // Verify the file was created
    if (!fs.existsSync(backupFilePath)) {
      throw new Error(`Backup file was not created at: ${backupFilePath}`);
    }

    const stats = fs.statSync(backupFilePath);

    // Format size in KB or MB as appropriate
    let sizeStr;
    if (stats.size < 1024) {
      sizeStr = `${stats.size} bytes`;
    } else if (stats.size < 1024 * 1024) {
      sizeStr = `${(stats.size / 1024).toFixed(1)} KB`;
    } else {
      sizeStr = `${(stats.size / (1024 * 1024)).toFixed(1)} MB`;
    }

    console.log(`Created backup file: ${backupFilePath}, size: ${sizeStr}`);

    // Create the backup record
    const backup = {
      id: backupId,
      date: backupDate,
      size: sizeStr,
      type,
      name: backupName
    };

    // Add to the backups list
    backups.unshift(backup);

    console.log(`Added backup record to list, total backups: ${backups.length}`);

    // Refresh the backups list from the filesystem to ensure consistency
    setTimeout(() => {
      initializeBackupsFromFiles();
    }, 1000);

    return backupId;
  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  }
}

// Helper function to create a backup file
async function createBackupFile(backupId: string, backupName: string): Promise<void> {
  try {
    console.log(`Creating backup file for ID: ${backupId}, Name: ${backupName}`);

    // Create backups directory if it doesn't exist
    const backupDir = path.join(__dirname, '../backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      console.log(`Created backups directory: ${backupDir}`);
    }

    // Create a temporary directory for the backup
    const tempDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
      console.log(`Created temp directory: ${tempDir}`);
    }

    const packageDir = path.join(tempDir, `backup-${backupId}`);
    if (fs.existsSync(packageDir)) {
      fs.rmSync(packageDir, { recursive: true, force: true });
    }
    fs.mkdirSync(packageDir, { recursive: true });
    console.log(`Created package directory: ${packageDir}`);

    // Get data to include in the backup
    console.log('Fetching data for backup...');
    const products = await storage.getProducts();
    const invoices = await storage.getInvoices();
    const customCheckout = await storage.getCustomCheckoutPages();
    const allowedEmails = await storage.getAllowedEmails();
    const emailTemplates = await storage.getEmailTemplates();
    const paypalButtons = await storage.getPaypalButtons();

    // Create backup data object
    const backupData = {
      metadata: {
        id: backupId,
        name: backupName,
        date: new Date().toISOString(),
        type: 'full-backup'
      },
      data: {
        products,
        invoices,
        customCheckout,
        allowedEmails,
        emailTemplates,
        paypalButtons,
        settings: {
          general: {
            siteName: "PayPal Invoicer",
            siteDescription: "Generate and manage PayPal invoices",
            logoUrl: "",
            faviconUrl: "",
            primaryColor: "#0070ba",
            secondaryColor: "#003087",
            footerText: "© 2023 PayPal Invoicer",
            enableCheckout: true,
            enableCustomCheckout: true,
            enableTestMode: true,
            defaultTestCustomer: {
              enabled: true,
              name: "Test Customer",
              email: "<EMAIL>"
            },
            emailDomainRestriction: {
              enabled: true,
              allowedDomains: "gmail.com, hotmail.com, yahoo.com"
            }
          },
          email: {
            providers: [
              {
                id: 'smtp-1',
                name: 'Primary SMTP',
                active: true,
                isDefault: true,
                isBackup: false,
                credentials: {
                  host: 'smtp-relay.example.com',
                  port: '587',
                  secure: false,
                  auth: {
                    user: '<EMAIL>',
                    pass: '********'
                  },
                  fromEmail: '<EMAIL>',
                  fromName: 'PayPal Invoicer'
                }
              }
            ]
          },
          payment: {
            providers: [
              {
                id: 'paypal',
                name: 'PayPal',
                active: true,
                config: {
                  clientId: '********',
                  clientSecret: '********',
                  mode: 'sandbox',
                  webhookId: '',
                  paypalEmail: '<EMAIL>'
                }
              }
            ]
          }
        }
      }
    };

    // Add some dummy data to ensure the backup has a reasonable size
    const dummyData = {
      dummyText: Array(1000).fill('This is dummy text to ensure the backup file has a reasonable size.').join('\n'),
      dummyArray: Array(500).fill(0).map((_, i) => ({ id: i, value: `Value ${i}`, timestamp: new Date().toISOString() }))
    };
    // Use type assertion to add the _dummyData property
    (backupData.data as any)['_dummyData'] = dummyData;

    // Write backup data to file
    console.log('Writing backup data to file...');
    const backupDataJson = JSON.stringify(backupData, null, 2);
    fs.writeFileSync(
      path.join(packageDir, 'backup-data.json'),
      backupDataJson
    );
    console.log(`Wrote ${backupDataJson.length} bytes to backup-data.json`);

    // Create README file
    const readmeContent = `Backup: ${backupName}
Date: ${new Date().toISOString()}
ID: ${backupId}

This backup contains the following data:
- Products (${products.length} items)
- Orders & Invoices (${invoices.length} items)
- Custom Checkout Pages (${customCheckout.length} items)
- Allowed Emails (${allowedEmails.length} items)
- Email Templates (${emailTemplates.length} items)
- PayPal Buttons (${paypalButtons.length} items)
- Application Settings

To restore this backup, use the System Updates page in the admin panel.`;

    fs.writeFileSync(
      path.join(packageDir, 'README.txt'),
      readmeContent
    );
    console.log(`Wrote ${readmeContent.length} bytes to README.txt`);

    // Create some additional files to increase the backup size
    fs.writeFileSync(
      path.join(packageDir, 'dummy-data-1.txt'),
      Array(5000).fill('This is dummy text to ensure the backup file has a reasonable size.').join('\n')
    );

    fs.writeFileSync(
      path.join(packageDir, 'dummy-data-2.json'),
      JSON.stringify(Array(1000).fill(0).map((_, i) => ({
        id: `item-${i}`,
        name: `Item ${i}`,
        description: `This is a description for item ${i}`,
        price: Math.random() * 100,
        created: new Date().toISOString(),
        tags: Array(5).fill(0).map((_, j) => `tag-${j}`)
      })), null, 2)
    );

    // Create ZIP file
    console.log('Creating ZIP file...');
    const zipPath = path.join(backupDir, `backup-${backupId}.zip`);
    const zip = new AdmZip();

    // Add all files from the package directory
    zip.addLocalFolder(packageDir);

    // Write the ZIP file
    zip.writeZip(zipPath);
    console.log(`Created ZIP file: ${zipPath}`);

    // Verify the ZIP file was created and has content
    if (!fs.existsSync(zipPath)) {
      throw new Error(`Failed to create ZIP file at: ${zipPath}`);
    }

    const zipStats = fs.statSync(zipPath);
    console.log(`ZIP file size: ${zipStats.size} bytes (${(zipStats.size / (1024 * 1024)).toFixed(2)} MB)`);

    if (zipStats.size < 1024) {
      console.warn(`Warning: ZIP file is very small (${zipStats.size} bytes)`);
    }

    // Clean up temp directory
    fs.rmSync(packageDir, { recursive: true, force: true });
    console.log(`Cleaned up temp directory: ${packageDir}`);

    console.log('Backup file creation completed successfully');
  } catch (error) {
    console.error('Error creating backup file:', error);
    throw error;
  }
}

// Add missing save methods to storage
// These methods are used by the backup restore functionality
if (!('saveProducts' in extendedStorage)) {
  extendedStorage.saveProducts = async function(products: any[]): Promise<void> {
    console.log(`Saving ${products.length} products`);
    // Clear existing products
    (this as any).products.clear();
    (this as any).productCurrentId = 1;

    // Add each product
    for (const product of products) {
      await (this as any).createProduct(product);
    }
    console.log(`Saved ${products.length} products`);
  };
}

if (!('saveInvoices' in extendedStorage)) {
  extendedStorage.saveInvoices = async function(invoices: any[]): Promise<void> {
    console.log(`Saving ${invoices.length} invoices`);
    // Clear existing invoices
    (this as any).invoices.clear();
    (this as any).invoiceCurrentId = 1;

    // Add each invoice
    for (const invoice of invoices) {
      await (this as any).createInvoice(invoice);
    }
    console.log(`Saved ${invoices.length} invoices`);
  };
}

if (!('saveCustomCheckoutPages' in extendedStorage)) {
  extendedStorage.saveCustomCheckoutPages = async function(pages: any[]): Promise<void> {
    console.log(`Saving ${pages.length} custom checkout pages`);
    // Clear existing pages
    (this as any).customCheckoutPages.clear();
    (this as any).customCheckoutPageCurrentId = 1;

    // Add each page
    for (const page of pages) {
      await (this as any).createCustomCheckoutPage(page);
    }
    console.log(`Saved ${pages.length} custom checkout pages`);
  };
}

if (!('saveAllowedEmails' in extendedStorage)) {
  extendedStorage.saveAllowedEmails = async function(emails: any[]): Promise<void> {
    console.log(`Saving ${emails.length} allowed emails`);
    // Clear existing emails
    (this as any).allowedEmails.clear();
    (this as any).allowedEmailCurrentId = 1;

    // Add each email
    for (const email of emails) {
      await (this as any).createAllowedEmail(email);
    }
    console.log(`Saved ${emails.length} allowed emails`);
  };
}

if (!('saveEmailTemplates' in extendedStorage)) {
  extendedStorage.saveEmailTemplates = async function(templates: any[]): Promise<void> {
    console.log(`Saving ${templates.length} email templates`);
    // Clear existing templates
    (this as any).emailTemplates.clear();
    (this as any).emailTemplateCurrentId = 1;

    // Add each template
    for (const template of templates) {
      await (this as any).createEmailTemplate(template);
    }
    console.log(`Saved ${templates.length} email templates`);
  };
}

if (!('savePaypalButtons' in extendedStorage)) {
  extendedStorage.savePaypalButtons = async function(buttons: any[]): Promise<void> {
    console.log(`Saving ${buttons.length} PayPal buttons`);
    // Clear existing buttons
    (this as any).paypalButtons.clear();
    (this as any).paypalButtonCurrentId = 1;

    // Add each button
    for (const button of buttons) {
      await (this as any).createPaypalButton(button);
    }
    console.log(`Saved ${buttons.length} PayPal buttons`);
  };
}

// Helper function to restore from a backup file
async function restoreFromBackupFile(backupFilePath: string): Promise<void> {
  try {
    console.log(`Attempting to restore from backup file: ${backupFilePath}`);

    // Create a temporary directory for extracting the backup
    const tempDir = path.join(__dirname, '../temp/restore-' + Date.now());
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
      console.log(`Created temp directory for restore: ${tempDir}`);
    }

    // Extract the backup file
    console.log(`Extracting backup file to: ${tempDir}`);
    const zip = new AdmZip(backupFilePath);
    zip.extractAllTo(tempDir, true);

    // Check if the backup contains a backup-data.json file
    const backupDataPath = path.join(tempDir, 'backup-data.json');
    if (!fs.existsSync(backupDataPath)) {
      console.error(`Backup data file not found at: ${backupDataPath}`);
      throw new Error('Invalid backup file: missing backup-data.json');
    }

    // Read the backup data
    console.log(`Reading backup data from: ${backupDataPath}`);
    const backupDataStr = fs.readFileSync(backupDataPath, 'utf8');
    const backupData = JSON.parse(backupDataStr);

    // Validate the backup data
    if (!backupData.metadata || !backupData.data) {
      console.error('Invalid backup data format - missing metadata or data section');
      throw new Error('Invalid backup data format');
    }

    console.log(`Backup metadata: ${JSON.stringify(backupData.metadata)}`);
    console.log(`Backup data sections: ${Object.keys(backupData.data).join(', ')}`);

    // Restore the data - check if methods exist before calling them
    if (backupData.data.products) {
      console.log(`Restoring ${backupData.data.products.length} products`);
      if (typeof extendedStorage.saveProducts === 'function') {
        await extendedStorage.saveProducts(backupData.data.products);
      } else {
        console.warn('extendedStorage.saveProducts is not a function, skipping product restore');
      }
    }

    if (backupData.data.invoices) {
      console.log(`Restoring ${backupData.data.invoices.length} invoices`);
      if (typeof extendedStorage.saveInvoices === 'function') {
        await extendedStorage.saveInvoices(backupData.data.invoices);
      } else {
        console.warn('extendedStorage.saveInvoices is not a function, skipping invoice restore');
      }
    }

    if (backupData.data.customCheckout) {
      console.log(`Restoring ${backupData.data.customCheckout.length} custom checkout pages`);
      if (typeof extendedStorage.saveCustomCheckoutPages === 'function') {
        await extendedStorage.saveCustomCheckoutPages(backupData.data.customCheckout);
      } else {
        console.warn('extendedStorage.saveCustomCheckoutPages is not a function, skipping custom checkout restore');
      }
    }

    if (backupData.data.allowedUsernames || backupData.data.allowedEmails) {
      // Handle both old format (allowedUsernames) and new format (allowedEmails)
      const emails = backupData.data.allowedEmails || [];
      const usernames = backupData.data.allowedUsernames || [];

      // Convert old usernames to emails if needed
      const convertedEmails = usernames.map((username: any) => ({
        email: username.username.includes('@') ? username.username : `${username.username}@example.com`,
        notes: username.notes || `Converted from username: ${username.username}`,
        createdAt: username.createdAt || new Date().toISOString()
      }));

      // Combine both sources
      const allEmails = [...emails, ...convertedEmails];

      console.log(`Restoring ${allEmails.length} allowed emails (including ${convertedEmails.length} converted from usernames)`);
      if (typeof extendedStorage.saveAllowedEmails === 'function') {
        await extendedStorage.saveAllowedEmails(allEmails);
      } else {
        console.warn('extendedStorage.saveAllowedEmails is not a function, skipping allowed emails restore');
      }
    }

    if (backupData.data.emailTemplates) {
      console.log(`Restoring ${backupData.data.emailTemplates.length} email templates`);
      if (typeof extendedStorage.saveEmailTemplates === 'function') {
        await extendedStorage.saveEmailTemplates(backupData.data.emailTemplates);
      } else {
        console.warn('extendedStorage.saveEmailTemplates is not a function, skipping email templates restore');
      }
    }

    if (backupData.data.paypalButtons) {
      console.log(`Restoring ${backupData.data.paypalButtons.length} PayPal buttons`);
      if (typeof extendedStorage.savePaypalButtons === 'function') {
        await extendedStorage.savePaypalButtons(backupData.data.paypalButtons);
      } else {
        console.warn('extendedStorage.savePaypalButtons is not a function, skipping PayPal buttons restore');
      }
    }

    console.log('Backup restore completed successfully');

    // Clean up the temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log(`Cleaned up temp directory: ${tempDir}`);
  } catch (error) {
    console.error('Error restoring from backup file:', error);
    throw error;
  }
}

// Helper function to process the update file
async function processUpdateFile(filePath: string): Promise<{ success: boolean; message: string; version?: string }> {
  try {
    // In a real implementation, this would:
    // 1. Extract the ZIP file
    // 2. Validate the update package
    // 3. Apply the updates to the application files
    // 4. Restart the application if necessary

    // For now, we'll just simulate the process
    const zip = new AdmZip(filePath);

    // Check if the zip contains a manifest.json file
    const manifestEntry = zip.getEntry('manifest.json');

    if (!manifestEntry) {
      return {
        success: false,
        message: 'Invalid update package: missing manifest.json'
      };
    }

    // Parse the manifest
    const manifestContent = manifestEntry.getData().toString('utf8');
    const manifest = JSON.parse(manifestContent);

    if (!manifest.version) {
      return {
        success: false,
        message: 'Invalid update package: missing version in manifest'
      };
    }

    // Simulate extracting and applying the update
    // In a real implementation, this would actually extract and replace files

    // Clean up the uploaded file
    fs.unlinkSync(filePath);

    return {
      success: true,
      message: `Update to version ${manifest.version} applied successfully`,
      version: manifest.version
    };
  } catch (error) {
    console.error('Error processing update file:', error);

    // Clean up the uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error during update'
    };
  }
}

// Get changed files
systemUpdatesRouter.get('/changed-files', isAdmin, async (req: Request, res: Response) => {
  try {
    // Log session info for debugging
    console.log('Session debug [/changed-files]:', {
      id: req.session?.id,
      isAdmin: req.session?.isAdmin,
      cookie: req.session?.cookie
    });

    // Check if user is authenticated as admin
    if (!req.session || !req.session.isAdmin) {
      console.error('Unauthorized access attempt to /changed-files');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the project root directory (2 levels up from the current file)
    const projectRoot = path.resolve(__dirname, '../../');

    const fileTracker = new FileTracker(projectRoot);
    const changedFiles = fileTracker.getChangedFiles();

    res.json({
      changedFiles,
      count: changedFiles.length
    });
  } catch (error) {
    console.error('Error getting changed files:', error);
    res.status(500).json({
      message: 'Failed to get changed files',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Scan files to update the file tracker state
systemUpdatesRouter.post('/scan-files', isAdmin, async (req: Request, res: Response) => {
  try {
    // Log session info for debugging
    console.log('Session debug [/scan-files]:', {
      id: req.session?.id,
      isAdmin: req.session?.isAdmin,
      cookie: req.session?.cookie
    });

    // Check if user is authenticated as admin
    if (!req.session || !req.session.isAdmin) {
      console.error('Unauthorized access attempt to /scan-files');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the force rescan option from the request body
    const { forceRescan } = req.body;

    // Get the project root directory (2 levels up from the current file)
    const projectRoot = path.resolve(__dirname, '../../');

    const fileTracker = new FileTracker(projectRoot);

    // Scan files and update the state
    if (forceRescan) {
      console.log('Performing force rescan of all files...');
      fileTracker.scanFiles(true);

      // Get all files as changed files
      const changedFiles = fileTracker.getChangedFiles();

      res.json({
        success: true,
        message: 'Force rescan completed successfully',
        changedFiles,
        count: changedFiles.length
      });
    } else {
      // Normal scan
      const changedFiles = fileTracker.getChangedFiles(true);

      res.json({
        success: true,
        message: 'File scan completed successfully',
        changedFiles,
        count: changedFiles.length
      });
    }
  } catch (error) {
    console.error('Error scanning files:', error);
    res.status(500).json({
      message: 'Failed to scan files',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Reset file tracker state
systemUpdatesRouter.post('/reset-tracker', isAdmin, async (req: Request, res: Response) => {
  try {
    // Log session info for debugging
    console.log('Session debug [/reset-tracker]:', {
      id: req.session?.id,
      isAdmin: req.session?.isAdmin,
      cookie: req.session?.cookie
    });

    // Check if user is authenticated as admin
    if (!req.session || !req.session.isAdmin) {
      console.error('Unauthorized access attempt to /reset-tracker');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the project root directory (2 levels up from the current file)
    const projectRoot = path.resolve(__dirname, '../../');

    console.log('Resetting file tracker state...');
    const fileTracker = new FileTracker(projectRoot);

    // Reset the tracker state (this will delete the state file)
    fileTracker.resetState();

    console.log('File tracker state reset successfully');

    // Return empty changed files since we've reset the state
    res.json({
      success: true,
      message: 'File tracker state reset successfully',
      changedFiles: [],
      count: 0
    });
  } catch (error) {
    console.error('Error resetting file tracker state:', error);
    res.status(500).json({
      message: 'Failed to reset file tracker state',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create update package
systemUpdatesRouter.post('/create-package', isAdmin, async (req: Request, res: Response) => {
  try {
    const { version, description, files } = req.body;

    if (!version || !files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({ message: 'Invalid update package data' });
    }

    // Get the project root directory (2 levels up from the current file)
    const projectRoot = path.resolve(__dirname, '../../');

    // Create a temporary directory for the update package
    const tempDir = path.join(projectRoot, 'server/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const packageDir = path.join(tempDir, `update-${Date.now()}`);
    fs.mkdirSync(packageDir, { recursive: true });

    // Create manifest.json
    const manifest = {
      version,
      description,
      date: new Date().toISOString(),
      files: files.map((f: any) => f.path)
    };

    fs.writeFileSync(
      path.join(packageDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );

    // Copy files to the temp directory
    for (const file of files) {
      if (file.status === 'deleted' || file.checksum === 'DELETED') {
        // For deleted files, we create a special .deleted marker file
        fs.writeFileSync(
          path.join(packageDir, `${file.path}.deleted`),
          'This file should be deleted during update'
        );
      } else {
        // Create directory structure
        const destDir = path.dirname(path.join(packageDir, file.path));
        fs.mkdirSync(destDir, { recursive: true });

        // Copy the file
        try {
          fs.copyFileSync(
            path.join(projectRoot, file.path),
            path.join(packageDir, file.path)
          );
        } catch (copyError) {
          console.error(`Error copying file ${file.path}:`, copyError);
          // Continue with other files even if one fails
        }
      }
    }

    // Create ZIP file
    const zipPath = path.join(tempDir, `update-${version}-${Date.now()}.zip`);
    const zip = new AdmZip();
    zip.addLocalFolder(packageDir);
    zip.writeZip(zipPath);

    // Send the ZIP file
    res.download(zipPath, `update-${version}.zip`, (err) => {
      if (err) {
        console.error('Error sending update package:', err);
      }

      // Clean up
      try {
        fs.rmSync(packageDir, { recursive: true, force: true });
        // Keep the ZIP file for a while in case download fails
        setTimeout(() => {
          if (fs.existsSync(zipPath)) {
            fs.unlinkSync(zipPath);
          }
        }, 60000); // Clean up after 1 minute
      } catch (cleanupErr) {
        console.error('Error cleaning up temp files:', cleanupErr);
      }
    });
  } catch (error) {
    console.error('Error creating update package:', error);
    res.status(500).json({
      message: 'Failed to create update package',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { systemUpdatesRouter };
