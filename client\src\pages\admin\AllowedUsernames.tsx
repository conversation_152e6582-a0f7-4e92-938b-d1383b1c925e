import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash, Upload } from 'lucide-react';
import { format } from 'date-fns';

// Form schema for adding a new username
const usernameSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  notes: z.string().optional(),
});

// Form schema for bulk import
const bulkImportSchema = z.object({
  usernames: z.string().min(1, 'Usernames are required'),
});

type UsernameFormValues = z.infer<typeof usernameSchema>;
type BulkImportFormValues = z.infer<typeof bulkImportSchema>;

export default function AllowedUsernamesPage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isBulkImportDialogOpen, setIsBulkImportDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedUsername, setSelectedUsername] = useState<any>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Query to fetch allowed usernames
  const { data: usernames, isLoading } = useQuery({
    queryKey: ['/api/allowed-usernames'],
    queryFn: () => apiRequest('/api/allowed-usernames', 'GET')
  });

  // Form for adding a new username
  const usernameForm = useForm<UsernameFormValues>({
    resolver: zodResolver(usernameSchema),
    defaultValues: {
      username: '',
      notes: '',
    }
  });

  // Form for bulk import
  const bulkImportForm = useForm<BulkImportFormValues>({
    resolver: zodResolver(bulkImportSchema),
    defaultValues: {
      usernames: '',
    }
  });

  // Mutation to add a new username
  const addUsernameMutation = useMutation({
    mutationFn: (data: UsernameFormValues) => {
      return apiRequest('/api/allowed-usernames', 'POST', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-usernames'] });
      setIsAddDialogOpen(false);
      usernameForm.reset();
      toast({
        title: 'Success',
        description: 'Username added successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add username',
        variant: 'destructive',
      });
    }
  });

  // Mutation to bulk import usernames
  const bulkImportMutation = useMutation({
    mutationFn: (data: BulkImportFormValues) => {
      return apiRequest('/api/allowed-usernames/bulk', 'POST', data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-usernames'] });
      setIsBulkImportDialogOpen(false);
      bulkImportForm.reset();
      toast({
        title: 'Success',
        description: data.message || 'Usernames imported successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to import usernames',
        variant: 'destructive',
      });
    }
  });

  // Mutation to delete a username
  const deleteUsernameMutation = useMutation({
    mutationFn: (id: number) => apiRequest(`/api/allowed-usernames/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/allowed-usernames'] });
      setIsDeleteDialogOpen(false);
      setSelectedUsername(null);
      toast({
        title: 'Success',
        description: 'Username deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete username',
        variant: 'destructive',
      });
    }
  });

  // Handle form submission for adding a new username
  const onAddSubmit = (values: UsernameFormValues) => {
    addUsernameMutation.mutate(values);
  };

  // Handle form submission for bulk import
  const onBulkImportSubmit = (values: BulkImportFormValues) => {
    bulkImportMutation.mutate(values);
  };

  // Open delete confirmation dialog
  const handleDeleteUsername = (username: any) => {
    setSelectedUsername(username);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDelete = () => {
    if (selectedUsername) {
      deleteUsernameMutation.mutate(selectedUsername.id);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  return (
    <AdminLayout>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Allowed Usernames</h1>
          <p className="text-muted-foreground">Manage usernames that are allowed to make purchases</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsBulkImportDialogOpen(true)} variant="outline">
            <Upload className="mr-2 h-4 w-4" /> Bulk Import
          </Button>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Username
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
        </div>
      ) : usernames && usernames.length > 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>Allowed Usernames</CardTitle>
            <CardDescription>
              These usernames are allowed to make purchases on checkout pages that require username validation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="py-3 px-4 text-left font-medium">Username</th>
                    <th className="py-3 px-4 text-left font-medium">Notes</th>
                    <th className="py-3 px-4 text-left font-medium">Created</th>
                    <th className="py-3 px-4 text-right font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {usernames.map((username: any) => (
                    <tr key={username.id} className="border-b">
                      <td className="py-3 px-4">{username.username}</td>
                      <td className="py-3 px-4">{username.notes || '-'}</td>
                      <td className="py-3 px-4">{formatDate(username.createdAt)}</td>
                      <td className="py-3 px-4 text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteUsername(username)}
                          className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="p-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
            <Upload className="h-6 w-6" />
          </div>
          <h3 className="mb-2 text-lg font-semibold">No usernames added yet</h3>
          <p className="mb-6 text-sm text-muted-foreground">
            Add usernames that are allowed to make purchases on checkout pages that require username validation.
          </p>
          <div className="flex justify-center gap-2">
            <Button onClick={() => setIsBulkImportDialogOpen(true)} variant="outline">
              <Upload className="mr-2 h-4 w-4" /> Bulk Import
            </Button>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Username
            </Button>
          </div>
        </Card>
      )}

      {/* Add Username Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Allowed Username</DialogTitle>
            <DialogDescription>
              Add a new username to the allowed list for checkout validation.
            </DialogDescription>
          </DialogHeader>

          <Form {...usernameForm}>
            <form onSubmit={usernameForm.handleSubmit(onAddSubmit)} className="space-y-4">
              <FormField
                control={usernameForm.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter username" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={usernameForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Add notes about this username" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="submit"
                  disabled={addUsernameMutation.isPending}
                >
                  {addUsernameMutation.isPending ? 'Adding...' : 'Add Username'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Bulk Import Dialog */}
      <Dialog open={isBulkImportDialogOpen} onOpenChange={setIsBulkImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Import Usernames</DialogTitle>
            <DialogDescription>
              Import multiple usernames at once. Enter one username per line, or separate them with commas or spaces.
            </DialogDescription>
          </DialogHeader>

          <Form {...bulkImportForm}>
            <form onSubmit={bulkImportForm.handleSubmit(onBulkImportSubmit)} className="space-y-4">
              <FormField
                control={bulkImportForm.control}
                name="usernames"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Usernames</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter usernames (one per line, or separated by commas or spaces)"
                        className="min-h-[200px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Example: username1, username2, username3
                      <br />
                      Or: username1 username2 username3
                      <br />
                      Or one username per line
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="submit"
                  disabled={bulkImportMutation.isPending}
                >
                  {bulkImportMutation.isPending ? 'Importing...' : 'Import Usernames'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Username</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the username "{selectedUsername?.username}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
