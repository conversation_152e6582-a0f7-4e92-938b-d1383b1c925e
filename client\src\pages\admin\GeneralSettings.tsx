import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getGeneralSettings, updateGeneralSettings, type GeneralSettings as GeneralSettingsType } from "@/api/generalSettings";
import AdminLayout from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import LogoFaviconUploader from "@/components/admin/LogoFaviconUploader";

// Define the form schema
const generalSettingsSchema = z.object({
  siteName: z.string().min(1, "Site name is required"),
  siteDescription: z.string().optional(),
  logoUrl: z.string().optional(),
  faviconUrl: z.string().optional(),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  footerText: z.string().optional(),
  enableCheckout: z.boolean().default(true),
  enableCustomCheckout: z.boolean().default(true),
  enableTestMode: z.boolean().default(true),
  defaultTestCustomer: z.object({
    enabled: z.boolean().default(true),
    name: z.string().min(1, "Customer name is required"),
    email: z.string().email("Please enter a valid email")
  }),
  emailDomainRestriction: z.object({
    enabled: z.boolean().default(true),
    allowedDomains: z.string().optional()
  })
});

type GeneralSettingsFormValues = z.infer<typeof generalSettingsSchema>;

export default function GeneralSettings() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Initialize form
  const form = useForm<GeneralSettingsFormValues>({
    resolver: zodResolver(generalSettingsSchema),
    defaultValues: {
      siteName: "",
      siteDescription: "",
      logoUrl: "",
      faviconUrl: "",
      primaryColor: "",
      secondaryColor: "",
      footerText: "",
      enableCheckout: true,
      enableCustomCheckout: true,
      enableTestMode: true,
      defaultTestCustomer: {
        enabled: true,
        name: "",
        email: ""
      },
      emailDomainRestriction: {
        enabled: true,
        allowedDomains: ""
      }
    }
  });

  // Fetch settings
  const { data: settingsData, isLoading, error } = useQuery({
    queryKey: ['generalSettings'],
    queryFn: getGeneralSettings,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Update form when data is loaded
  useEffect(() => {
    if (settingsData) {
      console.log('Loading settings data:', settingsData);
      form.reset(settingsData);
    }
  }, [settingsData, form]);

  // Show error toast when query fails
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Failed to load settings",
        variant: "destructive"
      });
    }
  }, [error, toast]);

  // Update settings mutation
  const { mutate, isPending: isSaving } = useMutation({
    mutationFn: updateGeneralSettings,
    onSuccess: (data) => {
      console.log('Settings saved successfully:', data);
      toast({
        title: "Success",
        description: "Settings saved successfully"
      });
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: ['generalSettings'] });
      queryClient.invalidateQueries({ queryKey: ['homepage'] });
      // Force refetch
      queryClient.refetchQueries({ queryKey: ['generalSettings'] });
    },
    onError: (error) => {
      console.error('Failed to save settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive"
      });
    }
  });

  // Form submission handler
  const onSubmit = (data: GeneralSettingsFormValues) => {
    console.log('Submitting form data:', data);
    mutate(data as GeneralSettingsType);
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">General Settings</CardTitle>
          <CardDescription>
            Configure your application general settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                <Tabs defaultValue="site">
                  <TabsList className="mb-6">
                    <TabsTrigger value="site">Site</TabsTrigger>
                    <TabsTrigger value="appearance">Appearance</TabsTrigger>
                    <TabsTrigger value="checkout">Checkout</TabsTrigger>
                    <TabsTrigger value="testing">Testing</TabsTrigger>
                    <TabsTrigger value="restrictions">Restrictions</TabsTrigger>
                  </TabsList>

                  {/* Site Settings */}
                  <TabsContent value="site" className="space-y-6">
                    <FormField
                      control={form.control}
                      name="siteName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Site Name</FormLabel>
                          <FormControl>
                            <Input placeholder="TemplateHub Pro" {...field} />
                          </FormControl>
                          <FormDescription>
                            The name of your site that will appear in the header and title
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="siteDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Site Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Premium productivity app templates and UI/UX design systems"
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormDescription>
                            A brief description of your site
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Footer Text</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="© 2024 TemplateHub Pro"
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormDescription>
                            Text to display in the footer
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TabsContent>

                  {/* Appearance Settings */}
                  <TabsContent value="appearance" className="space-y-6">
                    <FormField
                      control={form.control}
                      name="logoUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo</FormLabel>
                          <FormControl>
                            <LogoFaviconUploader
                              initialUrl={field.value}
                              onImageUploaded={field.onChange}
                              type="logo"
                              label="Logo"
                            />
                          </FormControl>
                          <FormDescription>
                            Logo displayed in the header. Leave empty to use default logo.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="faviconUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Favicon</FormLabel>
                          <FormControl>
                            <LogoFaviconUploader
                              initialUrl={field.value}
                              onImageUploaded={field.onChange}
                              type="favicon"
                              label="Favicon"
                            />
                          </FormControl>
                          <FormDescription>
                            Small icon displayed in browser tabs and bookmarks.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="primaryColor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Primary Color</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input
                                  type="text"
                                  placeholder="#0070ba"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormControl>
                                <Input
                                  type="color"
                                  className="w-12 p-1 h-10"
                                  {...field}
                                  value={field.value || "#0070ba"}
                                />
                              </FormControl>
                            </div>
                            <FormDescription>
                              Primary color for buttons and accents
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="secondaryColor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Secondary Color</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input
                                  type="text"
                                  placeholder="#003087"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormControl>
                                <Input
                                  type="color"
                                  className="w-12 p-1 h-10"
                                  {...field}
                                  value={field.value || "#003087"}
                                />
                              </FormControl>
                            </div>
                            <FormDescription>
                              Secondary color for highlights and backgrounds
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </TabsContent>

                  {/* Checkout Settings */}
                  <TabsContent value="checkout" className="space-y-6">
                    <FormField
                      control={form.control}
                      name="enableCheckout"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Enable Checkout</FormLabel>
                            <FormDescription>
                              Allow customers to checkout and purchase products
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="enableCustomCheckout"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Enable Custom Checkout</FormLabel>
                            <FormDescription>
                              Allow custom checkout pages with unique URLs
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TabsContent>

                  {/* Testing Settings */}
                  <TabsContent value="testing" className="space-y-6">
                    <FormField
                      control={form.control}
                      name="enableTestMode"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Enable Test Mode</FormLabel>
                            <FormDescription>
                              Enable testing features and sandbox environments
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <Separator className="my-4" />

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Default Test Customer</h3>

                      <FormField
                        control={form.control}
                        name="defaultTestCustomer.enabled"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Enable Default Test Customer</FormLabel>
                              <FormDescription>
                                Use a default test customer for testing
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {form.watch("defaultTestCustomer.enabled") && (
                        <div className="grid grid-cols-2 gap-4 p-4 border rounded-lg">
                          <FormField
                            control={form.control}
                            name="defaultTestCustomer.name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Customer Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="Test Customer" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="defaultTestCustomer.email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Customer Email</FormLabel>
                                <FormControl>
                                  <Input placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Restrictions Settings */}
                  <TabsContent value="restrictions" className="space-y-6">
                    <FormField
                      control={form.control}
                      name="emailDomainRestriction.enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Restrict Email Domains</FormLabel>
                            <FormDescription>
                              Only allow specific email domains for checkout
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {form.watch("emailDomainRestriction.enabled") && (
                      <FormField
                        control={form.control}
                        name="emailDomainRestriction.allowedDomains"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Allowed Email Domains</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="gmail.com, hotmail.com, yahoo.com"
                                {...field}
                                value={field.value || ""}
                              />
                            </FormControl>
                            <FormDescription>
                              Comma-separated list of allowed email domains
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </TabsContent>
                </Tabs>

                <div className="flex justify-end">
                  <Button type="submit" disabled={isSaving}>
                    {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Save Settings
                  </Button>
                </div>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
