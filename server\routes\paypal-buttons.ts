import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { insertPaypalButtonSchema } from '@shared/schema';

export const paypalButtonsRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get all PayPal buttons
paypalButtonsRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const buttons = await storage.getPaypalButtons();
    res.json(buttons);
  } catch (error) {
    console.error('Error fetching PayPal buttons:', error);
    res.status(500).json({ message: 'Failed to fetch PayPal buttons' });
  }
});

// Get a specific PayPal button
paypalButtonsRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid button ID' });
    }

    const button = await storage.getPaypalButton(id);
    if (!button) {
      return res.status(404).json({ message: 'Button not found' });
    }

    res.json(button);
  } catch (error) {
    console.error('Error fetching PayPal button:', error);
    res.status(500).json({ message: 'Failed to fetch PayPal button' });
  }
});

// Create a new PayPal button
paypalButtonsRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Validate the request body
    const buttonData = {
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    const validatedData = insertPaypalButtonSchema.parse(buttonData);
    
    // Create the button
    const button = await storage.createPaypalButton(validatedData);
    
    res.status(201).json(button);
  } catch (error) {
    console.error('Error creating PayPal button:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to create PayPal button' });
  }
});

// Update a PayPal button
paypalButtonsRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid button ID' });
    }
    
    // Check if the button exists
    const existingButton = await storage.getPaypalButton(id);
    if (!existingButton) {
      return res.status(404).json({ message: 'Button not found' });
    }
    
    // Update the button
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    const updatedButton = await storage.updatePaypalButton(id, updateData);
    
    res.json(updatedButton);
  } catch (error) {
    console.error('Error updating PayPal button:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    
    res.status(500).json({ message: 'Failed to update PayPal button' });
  }
});

// Delete a PayPal button
paypalButtonsRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid button ID' });
    }
    
    // Check if the button exists
    const existingButton = await storage.getPaypalButton(id);
    if (!existingButton) {
      return res.status(404).json({ message: 'Button not found' });
    }
    
    // Delete the button
    const success = await storage.deletePaypalButton(id);
    
    if (success) {
      res.json({ message: 'Button deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete button' });
    }
  } catch (error) {
    console.error('Error deleting PayPal button:', error);
    res.status(500).json({ message: 'Failed to delete PayPal button' });
  }
});
