import { Router, Request, Response } from 'express';
import { randomBytes, createHash } from 'crypto';
import { storage } from '../storage';
import { sendCustomEmail } from '../services/email';
import { authenticator } from 'otplib';
import * as qrcode from 'qrcode';

const authRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    rememberMe: boolean;
    userId: number;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
  }
}

// Forgot password
authRouter.post('/forgot-password', async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Get the user by email
    const user = await storage.getUserByEmail(email);

    // If no user is found, we still return success for security reasons
    if (!user) {
      console.log(`No user found with email: ${email}`);
      return res.status(200).json({
        message: 'If an account exists with this email, a password reset link will be sent.'
      });
    }

    // Generate a reset token
    const token = randomBytes(32).toString('hex');
    const tokenExpiry = new Date();
    tokenExpiry.setHours(tokenExpiry.getHours() + 1); // Token expires in 1 hour

    // Save the reset token to the user
    await storage.saveResetToken(user.id, token, tokenExpiry);

    // Send the reset email
    const resetUrl = `${req.protocol}://${req.get('host')}/admin/reset-password?token=${token}`;

    await sendCustomEmail(
      user.email,
      'Password Reset Request',
      `
        <p>You requested a password reset.</p>
        <p>Please click the link below to reset your password:</p>
        <p><a href="${resetUrl}">Reset Password</a></p>
        <p>This link will expire in 1 hour.</p>
      `
    );

    res.status(200).json({
      message: 'If an account exists with this email, a password reset link will be sent.'
    });
  } catch (error) {
    console.error('Error in forgot password:', error);
    res.status(500).json({
      message: 'An error occurred while processing your request.'
    });
  }
});

// Validate reset token
authRouter.post('/validate-reset-token', async (req: Request, res: Response) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required', valid: false });
    }

    // Check if the token exists and is valid
    const isValid = await storage.validateResetToken(token);

    res.status(200).json({ valid: isValid });
  } catch (error) {
    console.error('Error validating reset token:', error);
    res.status(500).json({
      message: 'An error occurred while validating the token.',
      valid: false
    });
  }
});

// Reset password
authRouter.post('/reset-password', async (req: Request, res: Response) => {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({ message: 'Token and password are required' });
    }

    // Validate the token
    const user = await storage.getUserByResetToken(token);

    if (!user) {
      return res.status(400).json({ message: 'Invalid or expired token' });
    }

    // Check if the token is expired
    if (new Date() > new Date(user.resetTokenExpiry)) {
      return res.status(400).json({ message: 'Token has expired' });
    }

    // Hash the new password
    const hashedPassword = createHash('sha256').update(password).digest('hex');

    // Update the user's password and clear the reset token
    await storage.updateUserPassword(user.id, hashedPassword);
    await storage.clearResetToken(user.id);

    res.status(200).json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({
      message: 'An error occurred while resetting your password.'
    });
  }
});

// Change username
authRouter.post('/change-username', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { currentPassword, newUsername } = req.body;

    if (!currentPassword || !newUsername) {
      return res.status(400).json({ message: 'Current password and new username are required' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify the current password
    const hashedPassword = createHash('sha256').update(currentPassword).digest('hex');
    if (user.password !== hashedPassword) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }

    // Check if the new username is already taken
    const existingUser = await storage.getUserByUsername(newUsername);
    if (existingUser && existingUser.id !== user.id) {
      return res.status(400).json({ message: 'Username is already taken' });
    }

    // Update the username
    await storage.updateUsername(user.id, newUsername);

    // Update the session
    req.session.username = newUsername;

    res.status(200).json({ message: 'Username updated successfully' });
  } catch (error) {
    console.error('Error changing username:', error);
    res.status(500).json({
      message: 'An error occurred while changing your username.'
    });
  }
});

// Change password
authRouter.post('/change-password', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Current password and new password are required' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify the current password
    const hashedCurrentPassword = createHash('sha256').update(currentPassword).digest('hex');
    if (user.password !== hashedCurrentPassword) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }

    // Hash the new password
    const hashedNewPassword = createHash('sha256').update(newPassword).digest('hex');

    // Update the password
    await storage.updateUserPassword(user.id, hashedNewPassword);

    res.status(200).json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({
      message: 'An error occurred while changing your password.'
    });
  }
});

// Auto-login settings
authRouter.get('/auto-login-settings', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      rememberMe: user.rememberMe || false
    });
  } catch (error) {
    console.error('Error getting auto-login settings:', error);
    res.status(500).json({
      message: 'An error occurred while getting your auto-login settings.'
    });
  }
});

authRouter.post('/auto-login-settings', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { rememberMe } = req.body;

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update the auto-login settings
    await storage.updateAutoLoginSettings(user.id, rememberMe);

    // Update the session
    req.session.rememberMe = rememberMe;

    // If remember me is enabled, set the session cookie to expire in 30 days
    if (rememberMe) {
      req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    } else {
      req.session.cookie.maxAge = 24 * 60 * 60 * 1000; // 24 hours (default)
    }

    res.status(200).json({
      message: 'Auto-login settings updated successfully',
      rememberMe
    });
  } catch (error) {
    console.error('Error updating auto-login settings:', error);
    res.status(500).json({
      message: 'An error occurred while updating your auto-login settings.'
    });
  }
});

// Generate 2FA secret and QR code
authRouter.post('/two-factor/setup', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Generate a new secret
    const secret = authenticator.generateSecret();

    // Generate a QR code
    const otpauth = authenticator.keyuri(user.username, 'Admin Dashboard', secret);
    const qrCodeUrl = await qrcode.toDataURL(otpauth);

    res.status(200).json({
      secret,
      qrCodeUrl
    });
  } catch (error) {
    console.error('Error setting up 2FA:', error);
    res.status(500).json({
      message: 'An error occurred while setting up two-factor authentication.'
    });
  }
});

// Verify and enable 2FA
authRouter.post('/two-factor/enable', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { secret, token } = req.body;

    if (!secret || !token) {
      return res.status(400).json({ message: 'Secret and token are required' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify the token
    const isValid = authenticator.verify({ token, secret });

    if (!isValid) {
      return res.status(400).json({ message: 'Invalid verification code' });
    }

    // Enable 2FA for the user
    await storage.enableTwoFactor(user.id, secret);

    res.status(200).json({
      message: 'Two-factor authentication enabled successfully'
    });
  } catch (error) {
    console.error('Error enabling 2FA:', error);
    res.status(500).json({
      message: 'An error occurred while enabling two-factor authentication.'
    });
  }
});

// Disable 2FA
authRouter.post('/two-factor/disable', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Disable 2FA for the user
    await storage.disableTwoFactor(user.id);

    res.status(200).json({
      message: 'Two-factor authentication disabled successfully'
    });
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    res.status(500).json({
      message: 'An error occurred while disabling two-factor authentication.'
    });
  }
});

// Verify 2FA token during login
authRouter.post('/two-factor/verify', async (req: Request, res: Response) => {
  try {
    // Check if user has started the 2FA process
    if (!req.session.requiresTwoFactor || !req.session.userId) {
      return res.status(400).json({ message: 'No two-factor authentication in progress' });
    }

    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Verification code is required' });
    }

    // Get the user
    const user = await storage.getUser(req.session.userId);

    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
      return res.status(400).json({ message: 'Two-factor authentication is not enabled for this user' });
    }

    // Verify the token
    const isValid = authenticator.verify({ token, secret: user.twoFactorSecret });

    if (!isValid) {
      return res.status(400).json({ message: 'Invalid verification code' });
    }

    // Mark 2FA as verified
    req.session.twoFactorVerified = true;

    // Complete the login process
    req.session.isAdmin = true;
    req.session.userId = user.id;

    // Track the device login
    try {
      const ip = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';
      const deviceName = userAgent.split(' ')[0] || 'Unknown Device';

      await storage.addDevice(user.id, {
        name: deviceName,
        ip,
        userAgent
      });
    } catch (deviceError) {
      console.error('Error tracking device login:', deviceError);
      // Continue with login even if device tracking fails
    }

    // Force session save and wait for it to complete
    req.session.save((err) => {
      if (err) {
        console.error('Error saving session:', err);
        return res.status(500).json({ message: 'Verification failed - session error' });
      }

      console.log('2FA verification successful, session updated:', {
        id: req.session.id,
        isAdmin: req.session.isAdmin,
        username: req.session.username,
        twoFactorVerified: req.session.twoFactorVerified,
        userId: req.session.userId
      });

      res.status(200).json({
        message: 'Two-factor authentication verified successfully',
        isAuthenticated: true,
        user: { username: req.session.username }
      });
    });
  } catch (error) {
    console.error('Error verifying 2FA:', error);
    res.status(500).json({
      message: 'An error occurred while verifying two-factor authentication.'
    });
  }
});

// Get 2FA status
authRouter.get('/two-factor/status', async (req: Request, res: Response) => {
  try {
    // Check if user is authenticated
    if (!req.session.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the current user
    const user = await storage.getUserByUsername(req.session.username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      enabled: user.twoFactorEnabled || false
    });
  } catch (error) {
    console.error('Error getting 2FA status:', error);
    res.status(500).json({
      message: 'An error occurred while getting two-factor authentication status.'
    });
  }
});

export default authRouter;
