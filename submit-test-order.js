import fetch from 'node-fetch';

async function submitTestOrder() {
  try {
    // First, get the checkout page details
    const checkoutResponse = await fetch('http://localhost:3001/api/custom-checkout/public/custom-link-checkout-test');
    const checkoutData = await checkoutResponse.json();
    console.log('Checkout page details:', checkoutData);

    // Submit a test order
    const orderResponse = await fetch('http://localhost:3001/api/custom-checkout/checkout/custom-link-checkout-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        fullName: 'Test Customer',
        email: '<EMAIL>',
        country: 'United States',
        appType: 'IPTV Smarters Pro'
      })
    });

    const orderData = await orderResponse.json();
    console.log('Order response:', orderData);

    // If successful, open the payment link
    if (orderData.paypalInvoiceUrl) {
      console.log('Payment link:', orderData.paypalInvoiceUrl);
      console.log('Please open the payment link in your browser to complete the payment.');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

submitTestOrder();
