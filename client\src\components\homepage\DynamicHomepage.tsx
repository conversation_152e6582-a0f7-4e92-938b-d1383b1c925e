import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getHomepageConfig, type HomepageConfig, type PageSection } from '@/api/homepage';
import { Product } from '@shared/schema';
import CheckoutForm from '@/components/CheckoutForm';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import HeroSection from './sections/HeroSection';
import FeaturesSection from './sections/FeaturesSection';
import ProductsSection from './sections/ProductsSection';
import CTASection from './sections/CTASection';
import { Skeleton } from '@/components/ui/skeleton';

interface DynamicHomepageProps {
  products?: Product[];
  isLoadingProducts?: boolean;
}

const DynamicHomepage: React.FC<DynamicHomepageProps> = ({ 
  products = [], 
  isLoadingProducts = false 
}) => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showCheckout, setShowCheckout] = useState(false);

  // Fetch homepage configuration
  const { data: homepageConfig, isLoading: isLoadingConfig, error } = useQuery<HomepageConfig>({
    queryKey: ['homepage-config'],
    queryFn: getHomepageConfig,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setShowCheckout(true);
    window.scrollTo(0, 0);
  };

  // Handle back to homepage
  const handleBackToHomepage = () => {
    setShowCheckout(false);
    setSelectedProduct(null);
  };

  // Render section based on type
  const renderSection = (section: PageSection) => {
    if (!section.enabled) return null;

    const commonProps = {
      key: section.id,
      section,
      theme: homepageConfig?.theme
    };

    switch (section.type) {
      case 'hero':
        return <HeroSection {...commonProps} />;
      
      case 'features':
        return <FeaturesSection {...commonProps} />;
      
      case 'products':
        return (
          <ProductsSection 
            {...commonProps}
            products={products}
            isLoading={isLoadingProducts}
            onSelectProduct={handleProductSelect}
          />
        );
      
      case 'cta':
        return <CTASection {...commonProps} />;
      
      case 'testimonials':
        // TODO: Implement TestimonialsSection
        return (
          <div key={section.id} className="py-16 bg-gray-50">
            <div className="container mx-auto px-4 text-center">
              <h2 className="text-3xl font-bold mb-4">Testimonials</h2>
              <p className="text-gray-600">Coming soon...</p>
            </div>
          </div>
        );
      
      case 'faq':
        // TODO: Implement FAQSection
        return (
          <div key={section.id} className="py-16">
            <div className="container mx-auto px-4 text-center">
              <h2 className="text-3xl font-bold mb-4">FAQ</h2>
              <p className="text-gray-600">Coming soon...</p>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  // Loading state
  if (isLoadingConfig) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow">
          {/* Hero skeleton */}
          <div className="py-20 px-4">
            <div className="container mx-auto text-center">
              <Skeleton className="h-12 w-3/4 mx-auto mb-4" />
              <Skeleton className="h-6 w-1/2 mx-auto mb-6" />
              <Skeleton className="h-4 w-2/3 mx-auto mb-8" />
              <Skeleton className="h-12 w-32 mx-auto" />
            </div>
          </div>
          
          {/* Features skeleton */}
          <div className="py-16 px-4">
            <div className="container mx-auto">
              <Skeleton className="h-8 w-1/3 mx-auto mb-12" />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {Array(3).fill(0).map((_, i) => (
                  <div key={i} className="text-center">
                    <Skeleton className="h-16 w-16 mx-auto mb-4 rounded-full" />
                    <Skeleton className="h-6 w-3/4 mx-auto mb-2" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-5/6 mx-auto" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    console.error('Error loading homepage configuration:', error);
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Homepage</h1>
            <p className="text-gray-600 mb-4">
              There was an error loading the homepage configuration.
            </p>
            <button 
              onClick={() => window.location.reload()} 
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show checkout form if product is selected
  if (showCheckout && selectedProduct) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow py-12 px-4">
          <CheckoutForm 
            product={selectedProduct}
            onBack={handleBackToHomepage}
          />
        </main>
        <Footer />
      </div>
    );
  }

  // Render dynamic homepage
  const sortedSections = homepageConfig?.sections
    ?.filter(section => section.enabled)
    ?.sort((a, b) => a.order - b.order) || [];

  return (
    <div className="min-h-screen flex flex-col">
      {/* SEO Meta Tags */}
      {homepageConfig?.seo && (
        <>
          <title>{homepageConfig.seo.title}</title>
          <meta name="description" content={homepageConfig.seo.description} />
          <meta name="keywords" content={homepageConfig.seo.keywords} />
          
          {/* Open Graph */}
          <meta property="og:title" content={homepageConfig.seo.ogTitle || homepageConfig.seo.title} />
          <meta property="og:description" content={homepageConfig.seo.ogDescription || homepageConfig.seo.description} />
          {homepageConfig.seo.ogImage && <meta property="og:image" content={homepageConfig.seo.ogImage} />}
          
          {/* Twitter */}
          <meta name="twitter:title" content={homepageConfig.seo.twitterTitle || homepageConfig.seo.title} />
          <meta name="twitter:description" content={homepageConfig.seo.twitterDescription || homepageConfig.seo.description} />
          {homepageConfig.seo.twitterImage && <meta name="twitter:image" content={homepageConfig.seo.twitterImage} />}
        </>
      )}

      {/* Dynamic CSS Variables for Theme */}
      {homepageConfig?.theme && (
        <style>{`
          :root {
            --homepage-primary-color: ${homepageConfig.theme.primaryColor};
            --homepage-secondary-color: ${homepageConfig.theme.secondaryColor};
            --homepage-accent-color: ${homepageConfig.theme.accentColor};
            --homepage-background-color: ${homepageConfig.theme.backgroundColor};
            --homepage-text-color: ${homepageConfig.theme.textColor};
            --homepage-font-family: ${homepageConfig.theme.fontFamily};
            --homepage-border-radius: ${homepageConfig.theme.borderRadius};
            --homepage-spacing: ${homepageConfig.theme.spacing};
          }
        `}</style>
      )}

      <Header />
      
      <main className="flex-grow">
        {sortedSections.map(renderSection)}
        
        {/* Fallback content if no sections */}
        {sortedSections.length === 0 && (
          <div className="py-20 px-4 text-center">
            <h1 className="text-3xl font-bold mb-4">Welcome</h1>
            <p className="text-gray-600 mb-8">
              No homepage sections configured. Please contact the administrator.
            </p>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default DynamicHomepage;
