import { Router, Request, Response } from 'express';
import { storage } from '../storage';
import puppeteer from 'puppeteer';
import { format } from 'date-fns';

const invoicesRouter = Router();

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);

  if (req.session.isAdmin) {
    console.log('Admin session verified:', true);
    next();
  } else {
    console.log('Admin session verified:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get invoice HTML
invoicesRouter.get('/:id/html', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    // Get the invoice
    const invoice = await storage.getInvoice(invoiceId);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Get the product
    const product = await storage.getProduct(invoice.productId);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Generate invoice HTML
    const invoiceHtml = generateInvoiceHtml(invoice, product);

    // Set content type and send the HTML
    res.setHeader('Content-Type', 'text/html');
    res.send(invoiceHtml);
  } catch (error) {
    console.error('Error generating invoice HTML:', error);
    res.status(500).json({ 
      message: 'Failed to generate invoice HTML',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get invoice PDF
invoicesRouter.get('/:id/pdf', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    // Get the invoice
    const invoice = await storage.getInvoice(invoiceId);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Get the product
    const product = await storage.getProduct(invoice.productId);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Generate invoice HTML
    const invoiceHtml = generateInvoiceHtml(invoice, product);

    // Generate PDF using puppeteer
    const browser = await puppeteer.launch({ headless: 'new' });
    const page = await browser.newPage();
    await page.setContent(invoiceHtml, { waitUntil: 'networkidle0' });
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px'
      }
    });

    await browser.close();

    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="invoice-${invoice.id}.pdf"`);
    
    // Send the PDF
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    res.status(500).json({ 
      message: 'Failed to generate invoice PDF',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to generate invoice HTML
function generateInvoiceHtml(invoice: any, product: any) {
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMMM dd, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  const formatCurrency = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return numAmount.toFixed(2);
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice #${invoice.id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .invoice-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 40px;
          border-bottom: 1px solid #eee;
          padding-bottom: 20px;
        }
        .invoice-title {
          font-size: 28px;
          color: #2563eb;
          margin-bottom: 5px;
        }
        .invoice-id {
          font-size: 16px;
          color: #666;
        }
        .invoice-status {
          display: inline-block;
          padding: 6px 12px;
          border-radius: 4px;
          font-weight: bold;
          text-transform: uppercase;
          font-size: 12px;
        }
        .status-paid {
          background-color: #dcfce7;
          color: #166534;
        }
        .status-pending {
          background-color: #fef9c3;
          color: #854d0e;
        }
        .status-cancelled {
          background-color: #fee2e2;
          color: #991b1b;
        }
        .invoice-meta {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .invoice-meta-section {
          flex: 1;
        }
        .meta-title {
          font-weight: bold;
          margin-bottom: 5px;
          color: #666;
        }
        .meta-content {
          margin-bottom: 15px;
        }
        .invoice-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .invoice-table th {
          background-color: #f9fafb;
          text-align: left;
          padding: 12px;
          border-bottom: 1px solid #e5e7eb;
        }
        .invoice-table td {
          padding: 12px;
          border-bottom: 1px solid #e5e7eb;
        }
        .invoice-table .amount {
          text-align: right;
        }
        .invoice-total {
          text-align: right;
          margin-top: 20px;
          margin-bottom: 40px;
        }
        .total-row {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 5px;
        }
        .total-label {
          width: 150px;
          text-align: right;
          margin-right: 20px;
        }
        .total-value {
          width: 100px;
          text-align: right;
          font-weight: bold;
        }
        .grand-total {
          font-size: 18px;
          font-weight: bold;
          color: #2563eb;
          border-top: 2px solid #e5e7eb;
          padding-top: 5px;
        }
        .invoice-notes {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #eee;
        }
        .notes-title {
          font-weight: bold;
          margin-bottom: 10px;
        }
        .notes-content {
          color: #666;
          font-style: italic;
        }
        .invoice-footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 12px;
        }
        @media print {
          body {
            padding: 0;
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-header">
        <div>
          <div class="invoice-title">INVOICE</div>
          <div class="invoice-id">Invoice #${invoice.id}</div>
        </div>
        <div>
          <div class="invoice-status status-${invoice.status.toLowerCase()}">
            ${invoice.status}
          </div>
          <div style="margin-top: 10px;">
            Date: ${formatDate(invoice.createdAt)}
          </div>
        </div>
      </div>

      <div class="invoice-meta">
        <div class="invoice-meta-section">
          <div class="meta-title">From</div>
          <div class="meta-content">
            Your Company Name<br>
            123 Business Street<br>
            Business City, 12345<br>
            <EMAIL>
          </div>
        </div>
        <div class="invoice-meta-section">
          <div class="meta-title">To</div>
          <div class="meta-content">
            ${invoice.customerName}<br>
            ${invoice.customerEmail}
          </div>
        </div>
      </div>

      <table class="invoice-table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th class="amount">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <div style="font-weight: bold;">${product.name}</div>
              <div style="color: #666; font-size: 14px;">${product.description}</div>
            </td>
            <td>1</td>
            <td>$${formatCurrency(invoice.amount)}</td>
            <td class="amount">$${formatCurrency(invoice.amount)}</td>
          </tr>
        </tbody>
      </table>

      <div class="invoice-total">
        <div class="total-row">
          <div class="total-label">Subtotal:</div>
          <div class="total-value">$${formatCurrency(invoice.amount)}</div>
        </div>
        <div class="total-row">
          <div class="total-label">Tax:</div>
          <div class="total-value">$0.00</div>
        </div>
        <div class="total-row grand-total">
          <div class="total-label">Total:</div>
          <div class="total-value">$${formatCurrency(invoice.amount)}</div>
        </div>
      </div>

      ${invoice.notes ? `
      <div class="invoice-notes">
        <div class="notes-title">Notes</div>
        <div class="notes-content">${invoice.notes}</div>
      </div>
      ` : ''}

      <div class="invoice-footer">
        Thank you for your business!
      </div>
    </body>
    </html>
  `;
}

export default invoicesRouter;
