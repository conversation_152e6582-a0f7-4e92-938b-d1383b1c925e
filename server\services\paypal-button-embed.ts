import { Product, CheckoutData } from '../../shared/schema';
import { getPaymentConfig } from '../config-storage';

/**
 * Interface for the PayPal button embed result
 */
export interface PayPalButtonEmbedResult {
  id: string;
  buttonHtml: string;
  isSimulated?: boolean;
  error?: string;
  status?: string;
  buttonName?: string;
}

/**
 * Test a specific PayPal button embed configuration
 */
export async function testPayPalButtonEmbedConnection(config: any): Promise<boolean> {
  try {
    // If testing a single button
    if (config.buttonHtml) {
      // Validate the button HTML
      const { buttonHtml, buttonName } = config;

      if (!buttonHtml) {
        console.error('PayPal button embed configuration is incomplete');
        return false;
      }

      // Check if the button HTML contains PayPal-related code
      if (!buttonHtml.includes('paypal') && !buttonHtml.includes('PayPal')) {
        console.error('Button HTML does not appear to be a valid PayPal button');
        return false;
      }

      return true;
    }
    // If testing the entire configuration with multiple buttons
    else if (config.buttons && Array.isArray(config.buttons)) {
      // Check if there are any active buttons
      const activeButtons = config.buttons.filter((button: any) => button.active);
      if (activeButtons.length === 0) {
        console.error('No active PayPal buttons found');
        return false;
      }

      // Validate each active button
      for (const button of activeButtons) {
        if (!button.buttonHtml) {
          console.error(`Button "${button.name}" is incomplete`);
          return false;
        }

        // Check if the button HTML contains PayPal-related code
        if (!button.buttonHtml.includes('paypal') && !button.buttonHtml.includes('PayPal')) {
          console.error(`Button "${button.name}" does not appear to be a valid PayPal button`);
          return false;
        }
      }

      return true;
    } else {
      console.error('Invalid PayPal button embed configuration');
      return false;
    }
  } catch (error) {
    console.error('Error testing PayPal button embed:', error);
    return false;
  }
}

/**
 * Get the next PayPal button to use based on the rotation method
 */
export function getNextPayPalButton(config: any): any {
  // Get all active buttons
  const activeButtons = config.buttons.filter((button: any) => button.active);

  if (activeButtons.length === 0) {
    throw new Error('No active PayPal buttons found');
  }

  // If there's only one active button, use it
  if (activeButtons.length === 1) {
    return activeButtons[0];
  }

  // Use the specified rotation method
  switch (config.rotationMethod) {
    case 'random':
      // Randomly select a button
      const randomIndex = Math.floor(Math.random() * activeButtons.length);
      return activeButtons[randomIndex];

    case 'round-robin':
    default:
      // Get the next button in sequence
      let nextIndex = (config.lastUsedIndex || 0) + 1;
      if (nextIndex >= activeButtons.length) {
        nextIndex = 0;
      }

      // Update the last used index
      config.lastUsedIndex = nextIndex;

      return activeButtons[nextIndex];
  }
}

/**
 * Create a PayPal button embed for a customer
 */
export async function createPayPalButtonEmbed(
  customerData: CheckoutData,
  product: Product,
  isTrial: boolean = false
): Promise<PayPalButtonEmbedResult> {
  try {
    // Get the PayPal button embed configuration
    const paymentConfig = getPaymentConfig();
    const providerId = isTrial ? 'trial-paypal-button-embed' : 'paypal-button-embed';
    const paypalButtonProvider = paymentConfig.providers.find(p => p.id === providerId);

    if (!paypalButtonProvider) {
      console.error(`${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider not found in configuration`);
      return {
        id: `SIMULATED-${Date.now()}`,
        buttonHtml: `<div>${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider is not configured</div>`,
        isSimulated: true,
        error: `${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider is not configured`
      };
    }

    if (!paypalButtonProvider.active) {
      console.error(`${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider is not active`);
      return {
        id: `SIMULATED-${Date.now()}`,
        buttonHtml: `<div>${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider is not active</div>`,
        isSimulated: true,
        error: `${isTrial ? 'Trial PayPal' : 'PayPal'} button embed provider is not active. Please activate it in Payment Settings.`
      };
    }

    const config = paypalButtonProvider.config as any;

    // Validate the configuration
    if (!config.buttons || !Array.isArray(config.buttons) || config.buttons.length === 0) {
      return {
        id: `SIMULATED-${Date.now()}`,
        buttonHtml: '<div>No PayPal buttons configured</div>',
        isSimulated: true,
        error: 'No PayPal buttons configured'
      };
    }

    // Get the next button to use
    let selectedButton;
    try {
      selectedButton = getNextPayPalButton(config);
    } catch (error) {
      return {
        id: `SIMULATED-${Date.now()}`,
        buttonHtml: '<div>Error selecting PayPal button</div>',
        isSimulated: true,
        error: error instanceof Error ? error.message : 'Error selecting PayPal button'
      };
    }

    // Generate a unique ID for this payment
    const paymentId = `PAYPAL-BUTTON-${Date.now()}`;

    // Get the button HTML
    let buttonHtml = selectedButton.buttonHtml;

    // Replace placeholders in the button HTML if they exist
    buttonHtml = buttonHtml
      .replace(/\{CUSTOMER_NAME\}/g, customerData.fullName)
      .replace(/\{CUSTOMER_EMAIL\}/g, customerData.email)
      .replace(/\{PRODUCT_NAME\}/g, product.name)
      .replace(/\{PRODUCT_ID\}/g, customerData.productId.toString())
      .replace(/\{AMOUNT\}/g, product.price.toString())
      .replace(/\{PAYMENT_ID\}/g, paymentId);

    return {
      id: paymentId,
      buttonHtml,
      status: 'pending',
      buttonName: selectedButton.name
    };
  } catch (error) {
    console.error('Error creating PayPal button embed:', error);
    return {
      id: `ERROR-${Date.now()}`,
      buttonHtml: '<div>Error creating PayPal button embed</div>',
      isSimulated: true,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
