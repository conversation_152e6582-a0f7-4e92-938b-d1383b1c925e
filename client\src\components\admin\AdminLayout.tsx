import React, { ReactNode, useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Settings,
  LogOut,
  Mail,
  CreditCard,
  Menu,
  X,
  Link as LinkIcon,
  Users,
  RefreshCw,
  User,
  Globe,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAdminAuth } from '@/hooks/use-admin-auth';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [location] = useLocation();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

  // Toggle sidebar when screen size changes
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Use admin auth hook
  const { logout, isAuthenticated, isLoading, checkAuth } = useAdminAuth();

  // Check auth on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const handleLogout = () => {
    logout();
  };

  const navItems = [
    {
      name: 'Dashboard',
      path: '/admin/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />
    },
    {
      name: 'Products',
      path: '/admin/products',
      icon: <Package className="h-5 w-5" />
    },
    {
      name: 'Homepage Editor',
      path: '/admin/homepage-editor',
      icon: <Globe className="h-5 w-5" />
    },
    {
      name: 'Orders',
      path: '/admin/orders',
      icon: <ShoppingCart className="h-5 w-5" />
    },
    {
      name: 'Trial Orders',
      path: '/admin/trial-orders',
      icon: <ShoppingCart className="h-5 w-5" />
    },

    {
      name: 'Custom Checkout',
      path: '/admin/custom-checkout',
      icon: <LinkIcon className="h-5 w-5" />
    },
    {
      name: 'Allowed Emails',
      path: '/admin/allowed-emails',
      icon: <Users className="h-5 w-5" />
    },


    {
      name: 'Email Settings',
      path: '/admin/email-settings',
      icon: <Mail className="h-5 w-5" />
    },
    {
      name: 'Payment Settings',
      path: '/admin/payment-settings',
      icon: <CreditCard className="h-5 w-5" />
    },
    {
      name: 'Settings',
      path: '/admin/settings',
      icon: <Settings className="h-5 w-5" />
    },
    {
      name: 'Account Settings',
      path: '/admin/account-settings',
      icon: <User className="h-5 w-5" />
    },
    {
      name: 'System Updates',
      path: '/admin/system-updates',
      icon: <RefreshCw className="h-5 w-5" />
    },
  ];

  const closeSidebar = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  return (
    <div className="flex h-screen bg-slate-50 overflow-hidden">
      {/* Mobile sidebar backdrop */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-20"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "bg-white shadow-md z-30 flex flex-col h-full transition-all",
          isMobile ? (
            sidebarOpen
              ? "fixed left-0 top-0 w-72"
              : "fixed -left-72 top-0 w-72"
          ) : (
            "w-64 relative"
          )
        )}
      >
        <div className="p-6 border-b flex items-center justify-between">
          <h1 className="text-xl font-bold text-primary">Admin Panel</h1>
          {isMobile && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>

        <nav className="flex-1 overflow-y-auto p-4">
          <ul className="space-y-1.5">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link href={item.path}>
                  <a
                    className={cn(
                      "flex items-center gap-3 rounded-md px-3 py-2 transition-colors text-sm",
                      location === item.path
                        ? "bg-primary/10 text-primary font-medium"
                        : "text-gray-600 hover:bg-slate-100"
                    )}
                    onClick={closeSidebar}
                  >
                    {item.icon}
                    {item.name}
                  </a>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-4 border-t mt-auto">
          <Button
            variant="ghost"
            className="w-full flex items-center justify-start gap-3 text-red-500 hover:text-red-600 hover:bg-red-50"
            onClick={handleLogout}
          >
            <LogOut className="h-5 w-5" />
            <span>Logout</span>
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white shadow-sm py-4 px-6 border-b flex items-center">
          <div className="flex items-center">
            {isMobile && (
              <Button
                variant="ghost"
                size="icon"
                className="mr-2"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <Menu className="h-5 w-5" />
              </Button>
            )}
            <h2 className="text-lg font-semibold">
              {navItems.find(item => item.path === location)?.name || 'Admin Panel'}
            </h2>
          </div>
        </header>

        <main className="p-6 overflow-auto flex-1">
          {children}
        </main>
      </div>
    </div>
  );
}