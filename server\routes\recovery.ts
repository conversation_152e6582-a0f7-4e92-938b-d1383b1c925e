import express from 'express';
import { storage } from '../storage';
import { isAdmin } from '../middleware/auth';
import { authenticator } from 'otplib';

const router = express.Router();

// Generate recovery codes for the current user
router.post('/api/admin/recovery-codes/generate', isAdmin, async (req, res) => {
  try {
    const userId = req.session.userId;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Check if 2FA is enabled
    const user = await storage.getUser(userId);
    if (!user || !user.twoFactorEnabled) {
      return res.status(400).json({ message: 'Two-factor authentication must be enabled to generate recovery codes' });
    }

    // Generate recovery codes
    const codes = await storage.generateRecoveryCodes(userId);
    res.status(200).json({ codes });
  } catch (error) {
    console.error('Error generating recovery codes:', error);
    res.status(500).json({ message: 'An error occurred while generating recovery codes' });
  }
});

// Verify a recovery code during 2FA login
router.post('/api/admin/recovery-codes/verify', async (req, res) => {
  try {
    const { code } = req.body;
    const username = req.session.username;

    if (!username) {
      return res.status(401).json({ message: 'Unauthorized - no active login session' });
    }

    // Get the user
    const user = await storage.getUserByUsername(username);
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized - user not found' });
    }

    // Verify the recovery code
    const isValid = await storage.verifyRecoveryCode(user.id, code);
    if (!isValid) {
      return res.status(400).json({ message: 'Invalid recovery code' });
    }

    // Complete the login process
    req.session.isAdmin = true;
    req.session.twoFactorVerified = true;
    req.session.userId = user.id;

    // Force session save and wait for it to complete
    req.session.save((err) => {
      if (err) {
        console.error('Error saving session:', err);
        return res.status(500).json({ message: 'Verification failed - session error' });
      }

      console.log('Recovery code verification successful, session updated:', {
        id: req.session.id,
        isAdmin: req.session.isAdmin,
        username: req.session.username,
        twoFactorVerified: req.session.twoFactorVerified
      });

      res.status(200).json({
        message: 'Recovery code verified successfully',
        isAuthenticated: true,
        user: { username: req.session.username }
      });
    });
  } catch (error) {
    console.error('Error verifying recovery code:', error);
    res.status(500).json({ message: 'An error occurred while verifying the recovery code' });
  }
});

export default router;
