import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertTriangle, CheckCircle2, Plus, Trash2, Edit, Code } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Schema for PayPal button embed settings
const paypalButtonEmbedSchema = z.object({
  active: z.boolean(),
  rotationMethod: z.enum(['round-robin', 'random']),
});

// Schema for adding/editing a PayPal button
const paypalButtonSchema = z.object({
  name: z.string().min(1, 'Button name is required'),
  buttonHtml: z.string().min(10, 'Button HTML is required'),
  description: z.string().optional(),
  active: z.boolean().default(true),
});

interface PayPalButtonEmbedSettingsProps {
  paymentConfig: any;
  refetch: () => void;
  providerId?: string;
  title?: string;
  description?: string;
}

export default function PayPalButtonEmbedSettings({
  paymentConfig,
  refetch,
  providerId = 'paypal-button-embed',
  title = 'PayPal Button Embed',
  description = 'Configure PayPal button embeds for invoice generation'
}: PayPalButtonEmbedSettingsProps) {
  const [isAddButtonDialogOpen, setIsAddButtonDialogOpen] = useState(false);
  const [isEditButtonDialogOpen, setIsEditButtonDialogOpen] = useState(false);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [selectedButtonId, setSelectedButtonId] = useState<string | null>(null);
  const [paypalButtons, setPaypalButtons] = useState<any[]>([]);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get the PayPal button embed provider from the config
  const paypalButtonEmbedProvider = paymentConfig?.providers.find((p: any) => p.id === providerId);

  // Default values for the main form
  const paypalButtonEmbedDefaults = paypalButtonEmbedProvider?.config || {
    buttons: [],
    rotationMethod: 'round-robin',
    lastUsedIndex: 0
  };

  // Update the buttons state when the config changes
  useEffect(() => {
    if (paypalButtonEmbedProvider?.config?.buttons) {
      setPaypalButtons(paypalButtonEmbedProvider.config.buttons);
    }
  }, [paypalButtonEmbedProvider]);

  // Main form for PayPal button embed settings
  const paypalButtonEmbedForm = useForm<z.infer<typeof paypalButtonEmbedSchema>>({
    resolver: zodResolver(paypalButtonEmbedSchema),
    defaultValues: {
      active: paypalButtonEmbedProvider?.active || false,
      rotationMethod: (paypalButtonEmbedDefaults.rotationMethod as 'round-robin' | 'random') || 'round-robin',
    }
  });

  // Form for adding a new PayPal button
  const addButtonForm = useForm<z.infer<typeof paypalButtonSchema>>({
    resolver: zodResolver(paypalButtonSchema),
    defaultValues: {
      name: '',
      buttonHtml: '',
      description: '',
      active: true,
    }
  });

  // Form for editing an existing PayPal button
  const editButtonForm = useForm<z.infer<typeof paypalButtonSchema>>({
    resolver: zodResolver(paypalButtonSchema),
    defaultValues: {
      name: '',
      buttonHtml: '',
      description: '',
      active: true,
    }
  });

  // Mutation to update PayPal button embed settings
  const updatePayPalButtonEmbedMutation = useMutation({
    mutationFn: (data: z.infer<typeof paypalButtonEmbedSchema>) =>
      apiRequest(`/api/admin/payment-config/${providerId}`, 'POST', data),
    onSuccess: () => {
      refetch();
      toast({
        title: "Settings updated",
        description: "PayPal Button Embed configuration has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to add a new PayPal button
  const addPayPalButtonMutation = useMutation({
    mutationFn: (data: z.infer<typeof paypalButtonSchema>) =>
      apiRequest(`/api/admin/payment-config/${providerId}/add`, 'POST', data),
    onSuccess: () => {
      refetch();
      setIsAddButtonDialogOpen(false);
      addButtonForm.reset();
      toast({
        title: "Button added",
        description: "PayPal button has been added successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to add button: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to edit an existing PayPal button
  const editPayPalButtonMutation = useMutation({
    mutationFn: (data: any) =>
      apiRequest(`/api/admin/payment-config/${providerId}/edit`, 'POST', {
        ...data,
        id: selectedButtonId
      }),
    onSuccess: () => {
      refetch();
      setIsEditButtonDialogOpen(false);
      setSelectedButtonId(null);
      editButtonForm.reset();
      toast({
        title: "Button updated",
        description: "PayPal button has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update button: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to delete a PayPal button
  const deletePayPalButtonMutation = useMutation({
    mutationFn: (id: string) =>
      apiRequest(`/api/admin/payment-config/${providerId}/delete`, 'POST', { id }),
    onSuccess: () => {
      refetch();
      toast({
        title: "Button deleted",
        description: "PayPal button has been deleted successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete button: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to test a PayPal button
  const testPayPalButtonMutation = useMutation({
    mutationFn: (data: any) =>
      apiRequest('/api/admin/payment-config/test', 'POST', {
        provider: providerId,
        config: data,
        testEmail
      }),
    onSuccess: (data) => {
      setTestResult({
        success: true,
        message: data.message
      });
    },
    onError: (error: Error) => {
      setTestResult({
        success: false,
        message: error.message
      });
    }
  });

  // Handle form submission for the main settings
  const onPayPalButtonEmbedSubmit = (data: z.infer<typeof paypalButtonEmbedSchema>) => {
    updatePayPalButtonEmbedMutation.mutate(data);
  };

  // Handle form submission for adding a new button
  const onAddButtonSubmit = (data: z.infer<typeof paypalButtonSchema>) => {
    addPayPalButtonMutation.mutate(data);
  };

  // Handle form submission for editing a button
  const onEditButtonSubmit = (data: z.infer<typeof paypalButtonSchema>) => {
    editPayPalButtonMutation.mutate(data);
  };

  // Handle button deletion
  const handleDeleteButton = (id: string) => {
    if (confirm('Are you sure you want to delete this button?')) {
      deletePayPalButtonMutation.mutate(id);
    }
  };

  // Handle button edit
  const handleEditButton = (button: any) => {
    setSelectedButtonId(button.id);
    editButtonForm.reset({
      name: button.name,
      buttonHtml: button.buttonHtml,
      description: button.description || '',
      active: button.active
    });
    setIsEditButtonDialogOpen(true);
  };

  // Handle test button
  const handleTestButton = (button: any) => {
    setSelectedButtonId(button.id);
    setTestEmail('');
    setTestResult(null);
    setIsTestDialogOpen(true);
  };

  // Handle test submission
  const handleTestSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!testEmail) {
      toast({
        title: "Error",
        description: "Please enter a test email address",
        variant: "destructive"
      });
      return;
    }

    const selectedButton = paypalButtons.find(button => button.id === selectedButtonId);
    if (!selectedButton) {
      toast({
        title: "Error",
        description: "Button not found",
        variant: "destructive"
      });
      return;
    }

    testPayPalButtonMutation.mutate({
      buttonHtml: selectedButton.buttonHtml
    });
  };

  return (
    <div className="space-y-6">
      <Form {...paypalButtonEmbedForm}>
        <form onSubmit={paypalButtonEmbedForm.handleSubmit(onPayPalButtonEmbedSubmit)} className="space-y-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <p className="text-sm text-gray-500">
              {description}
            </p>
            <FormField
              control={paypalButtonEmbedForm.control}
              name="active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-4 md:mt-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">
                    {field.value ? 'Active' : 'Inactive'}
                  </FormLabel>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={paypalButtonEmbedForm.control}
            name="rotationMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Button Rotation Method</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select rotation method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="round-robin">Round Robin</SelectItem>
                    <SelectItem value="random">Random</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose how PayPal buttons are rotated when multiple buttons are configured
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" disabled={updatePayPalButtonEmbedMutation.isPending}>
            {updatePayPalButtonEmbedMutation.isPending ? 'Saving...' : 'Save Settings'}
          </Button>
        </form>
      </Form>

      <div className="border-t pt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">{title} Buttons</h3>
          <Dialog open={isAddButtonDialogOpen} onOpenChange={setIsAddButtonDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-1">
                <Plus className="h-4 w-4" />
                <span>Add Button</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add {title} Button</DialogTitle>
                <DialogDescription>
                  Add a new {title.toLowerCase()} button to use in invoices
                </DialogDescription>
              </DialogHeader>
              <Form {...addButtonForm}>
                <form onSubmit={addButtonForm.handleSubmit(onAddButtonSubmit)} className="space-y-4">
                  <FormField
                    control={addButtonForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Button Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter button name" {...field} />
                        </FormControl>
                        <FormDescription>
                          A name to identify this button
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addButtonForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter button description" {...field} />
                        </FormControl>
                        <FormDescription>
                          A brief description of this button
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addButtonForm.control}
                    name="buttonHtml"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Button HTML</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Paste your PayPal button HTML code here"
                            className="font-mono text-xs h-40"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The HTML code for your PayPal button. You can use these placeholders:
                          <ul className="list-disc list-inside text-xs mt-1">
                            <li>{'{CUSTOMER_NAME}'} - Customer's name</li>
                            <li>{'{CUSTOMER_EMAIL}'} - Customer's email</li>
                            <li>{'{PRODUCT_NAME}'} - Product name</li>
                            <li>{'{PRODUCT_ID}'} - Product ID</li>
                            <li>{'{AMOUNT}'} - Payment amount</li>
                            <li>{'{PAYMENT_ID}'} - Unique payment ID</li>
                          </ul>
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addButtonForm.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {field.value ? 'Active' : 'Inactive'}
                        </FormLabel>
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsAddButtonDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={addPayPalButtonMutation.isPending}>
                      {addPayPalButtonMutation.isPending ? 'Adding...' : 'Add Button'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        {paypalButtons.length === 0 ? (
          <div className="text-center py-8 border rounded-md bg-muted/20">
            <p className="text-muted-foreground">No PayPal buttons configured yet</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => setIsAddButtonDialogOpen(true)}
            >
              Add Your First Button
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {paypalButtons.map((button) => (
              <Card key={button.id} className={`${!button.active ? 'opacity-70' : ''}`}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base flex items-center gap-2">
                        {button.name}
                        {!button.active && (
                          <Badge variant="outline" className="text-xs font-normal">
                            Inactive
                          </Badge>
                        )}
                      </CardTitle>
                      {button.description && (
                        <CardDescription>{button.description}</CardDescription>
                      )}
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleTestButton(button)}
                        title="Test Button"
                      >
                        <CheckCircle2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditButton(button)}
                        title="Edit Button"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteButton(button.id)}
                        title="Delete Button"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted/30 p-2 rounded-md">
                    <code className="text-xs font-mono break-all line-clamp-2">
                      {button.buttonHtml.substring(0, 100)}...
                    </code>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Edit Button Dialog */}
      <Dialog open={isEditButtonDialogOpen} onOpenChange={setIsEditButtonDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit PayPal Button</DialogTitle>
            <DialogDescription>
              Update your PayPal button settings
            </DialogDescription>
          </DialogHeader>
          <Form {...editButtonForm}>
            <form onSubmit={editButtonForm.handleSubmit(onEditButtonSubmit)} className="space-y-4">
              <FormField
                control={editButtonForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter button name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter button description" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="buttonHtml"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button HTML</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Paste your PayPal button HTML code here"
                        className="font-mono text-xs h-40"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The HTML code for your PayPal button
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      {field.value ? 'Active' : 'Inactive'}
                    </FormLabel>
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditButtonDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={editPayPalButtonMutation.isPending}>
                  {editPayPalButtonMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Test Button Dialog */}
      <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Test PayPal Button</DialogTitle>
            <DialogDescription>
              Send a test email with this PayPal button
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleTestSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="testEmail" className="text-sm font-medium">
                Test Email
              </label>
              <Input
                id="testEmail"
                type="email"
                placeholder="Enter your email address"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                required
              />
              <p className="text-xs text-muted-foreground">
                We'll send a test email with this PayPal button to this address
              </p>
            </div>

            {testResult && (
              <div className={`p-3 rounded-md ${testResult.success ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                <div className="flex items-start gap-2">
                  {testResult.success ? (
                    <CheckCircle2 className="h-5 w-5 mt-0.5" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 mt-0.5" />
                  )}
                  <div>
                    <p className="font-medium">{testResult.success ? 'Success!' : 'Error!'}</p>
                    <p className="text-sm">{testResult.message}</p>
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsTestDialogOpen(false)}>
                Close
              </Button>
              <Button type="submit" disabled={testPayPalButtonMutation.isPending}>
                {testPayPalButtonMutation.isPending ? 'Sending...' : 'Send Test Email'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
