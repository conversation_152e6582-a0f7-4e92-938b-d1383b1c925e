import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Loader2, Plus, Trash, Edit, Check, X, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Form schema for PayPal Button Embed settings
const formSchema = z.object({
  active: z.boolean().default(false),
  rotationMethod: z.enum(['round-robin', 'random']).default('round-robin'),
});

// Form schema for adding/editing a PayPal button
const buttonFormSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Button name is required'),
  buttonHtml: z.string().min(1, 'Button HTML is required'),
  description: z.string().optional(),
  active: z.boolean().default(true),
});

type PayPalButtonEmbedSettingsProps = {
  paymentConfig: any;
  refetch: () => void;
};

export default function PayPalButtonEmbedSettings({ paymentConfig, refetch }: PayPalButtonEmbedSettingsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isTestLoading, setIsTestLoading] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedButton, setSelectedButton] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('settings');

  // Get the PayPal Button Embed provider from the payment config
  const provider = paymentConfig?.providers?.find((p: any) => p.id === 'paypal-button-embed');
  const buttons = provider?.config?.buttons || [];
  const isActive = provider?.active || false;
  const rotationMethod = provider?.config?.rotationMethod || 'round-robin';

  // Create form for main settings
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      active: isActive,
      rotationMethod: rotationMethod,
    },
  });

  // Create form for adding a new button
  const addButtonForm = useForm<z.infer<typeof buttonFormSchema>>({
    resolver: zodResolver(buttonFormSchema),
    defaultValues: {
      name: '',
      buttonHtml: '',
      description: '',
      active: true,
    },
  });

  // Create form for editing a button
  const editButtonForm = useForm<z.infer<typeof buttonFormSchema>>({
    resolver: zodResolver(buttonFormSchema),
    defaultValues: {
      id: '',
      name: '',
      buttonHtml: '',
      description: '',
      active: true,
    },
  });

  // Handle form submission for main settings
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/payment-config/paypal-button-embed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      if (response.ok) {
        toast.success('PayPal Button Embed settings updated successfully');
        refetch();
      } else {
        toast.error(`Failed to update settings: ${data.message}`);
      }
    } catch (error) {
      toast.error(`Error updating settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle adding a new button
  const onAddButton = async (values: z.infer<typeof buttonFormSchema>) => {
    try {
      const response = await fetch('/api/admin/payment-config/paypal-button-embed/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      if (response.ok) {
        toast.success('PayPal button added successfully');
        setIsAddDialogOpen(false);
        addButtonForm.reset();
        refetch();
      } else {
        toast.error(`Failed to add button: ${data.message}`);
      }
    } catch (error) {
      toast.error(`Error adding button: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle editing a button
  const onEditButton = async (values: z.infer<typeof buttonFormSchema>) => {
    try {
      const response = await fetch('/api/admin/payment-config/paypal-button-embed/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();
      if (response.ok) {
        toast.success('PayPal button updated successfully');
        setIsEditDialogOpen(false);
        editButtonForm.reset();
        refetch();
      } else {
        toast.error(`Failed to update button: ${data.message}`);
      }
    } catch (error) {
      toast.error(`Error updating button: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle deleting a button
  const onDeleteButton = async (id: string) => {
    if (!confirm('Are you sure you want to delete this button?')) {
      return;
    }

    try {
      const response = await fetch('/api/admin/payment-config/paypal-button-embed/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id }),
      });

      const data = await response.json();
      if (response.ok) {
        toast.success('PayPal button deleted successfully');
        refetch();
      } else {
        toast.error(`Failed to delete button: ${data.message}`);
      }
    } catch (error) {
      toast.error(`Error deleting button: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle testing a button
  const onTestButton = async (buttonHtml: string) => {
    setIsTestLoading(true);
    try {
      const response = await fetch('/api/admin/test-payment-provider', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'paypal-button-embed',
          config: {
            buttonHtml
          },
          testEmail: '<EMAIL>'
        }),
      });

      const data = await response.json();
      if (response.ok && data.success) {
        toast.success('Test email sent successfully');
      } else {
        toast.error(`Test failed: ${data.message}`);
      }
    } catch (error) {
      toast.error(`Error testing button: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestLoading(false);
    }
  };

  // Open the edit dialog with the selected button data
  const openEditDialog = (button: any) => {
    setSelectedButton(button);
    editButtonForm.reset({
      id: button.id,
      name: button.name,
      buttonHtml: button.buttonHtml,
      description: button.description || '',
      active: button.active,
    });
    setIsEditDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="buttons">Buttons ({buttons.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Activate PayPal Button Embed</FormLabel>
                      <FormDescription>
                        When active, this will be used as the default payment method for new orders.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rotationMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button Rotation Method</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select rotation method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="round-robin">Round Robin</SelectItem>
                        <SelectItem value="random">Random</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose how to rotate between multiple PayPal buttons when sending invoices.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Settings'
                )}
              </Button>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="buttons" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">PayPal Buttons</h3>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Button
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add PayPal Button</DialogTitle>
                  <DialogDescription>
                    Add a new PayPal button that can be embedded in invoice emails.
                  </DialogDescription>
                </DialogHeader>
                <Form {...addButtonForm}>
                  <form onSubmit={addButtonForm.handleSubmit(onAddButton)} className="space-y-4">
                    <FormField
                      control={addButtonForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter button name" {...field} />
                          </FormControl>
                          <FormDescription>
                            A descriptive name for this button.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={addButtonForm.control}
                      name="buttonHtml"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button HTML</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Paste PayPal button HTML code here"
                              className="font-mono text-sm h-32"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Paste the HTML code for your PayPal button. You can use placeholders like {'{PRODUCT_NAME}'}, {'{AMOUNT}'}, and {'{PAYMENT_ID}'}.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={addButtonForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter button description"
                              className="h-20"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Optional description for this button.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={addButtonForm.control}
                      name="active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Active</FormLabel>
                            <FormDescription>
                              When active, this button can be used in invoices.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <DialogFooter>
                      <Button type="submit">Add Button</Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {buttons.length === 0 ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No PayPal buttons have been added yet. Add a button to get started.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid gap-4">
              {buttons.map((button: any) => (
                <Card key={button.id} className={button.active ? '' : 'opacity-60'}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{button.name}</h4>
                          {button.active ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                              Inactive
                            </Badge>
                          )}
                        </div>
                        {button.description && (
                          <p className="text-sm text-muted-foreground">{button.description}</p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onTestButton(button.buttonHtml)}
                          disabled={isTestLoading}
                        >
                          {isTestLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            'Test'
                          )}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openEditDialog(button)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => onDeleteButton(button.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-4 p-3 bg-gray-50 rounded-md overflow-x-auto">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                        {button.buttonHtml.length > 100
                          ? `${button.buttonHtml.substring(0, 100)}...`
                          : button.buttonHtml}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Edit Button Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit PayPal Button</DialogTitle>
            <DialogDescription>
              Update the PayPal button settings.
            </DialogDescription>
          </DialogHeader>
          <Form {...editButtonForm}>
            <form onSubmit={editButtonForm.handleSubmit(onEditButton)} className="space-y-4">
              <FormField
                control={editButtonForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter button name" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this button.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="buttonHtml"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Button HTML</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Paste PayPal button HTML code here"
                        className="font-mono text-sm h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Paste the HTML code for your PayPal button. You can use placeholders like {'{PRODUCT_NAME}'}, {'{AMOUNT}'}, and {'{PAYMENT_ID}'}.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter button description"
                        className="h-20"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description for this button.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editButtonForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active</FormLabel>
                      <FormDescription>
                        When active, this button can be used in invoices.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <input type="hidden" {...editButtonForm.register('id')} />
              <DialogFooter>
                <Button type="submit">Update Button</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
