import React from 'react';
import { OrderSummaryProps } from '@/lib/types';

const OrderSummary: React.FC<OrderSummaryProps> = ({ product }) => {
  const price = parseFloat(product.price.toString()).toFixed(2);
  
  return (
    <div className="mb-6 pb-6 border-b border-border">
      <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
      <div className="flex items-start">
        <img 
          src={product.imageUrl} 
          alt={product.name} 
          className="w-16 h-16 object-cover rounded mr-4" 
        />
        <div className="flex-grow">
          <h4 className="font-medium">{product.name}</h4>
          <p className="text-muted-foreground text-sm mb-2">Digital Product</p>
          <div className="flex justify-between">
            <span className="text-sm">Qty: 1</span>
            <span className="font-semibold">${price}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
