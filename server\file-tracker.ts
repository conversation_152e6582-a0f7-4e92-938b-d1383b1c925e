import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export interface FileRecord {
  path: string;
  checksum: string;
  lastModified: Date;
  status?: 'new' | 'modified' | 'deleted' | 'unchanged';
}

class FileTracker {
  private fileRecords: Map<string, FileRecord> = new Map();
  private basePath: string;
  private ignorePatterns: string[] = [
    'node_modules',
    '.git',
    'dist',
    'build',
    '.env',
    '.DS_Store',
    'temp',
    'uploads',
    '*.log',
    'server/data/file-tracker-state.json' // Explicitly ignore the tracker state file
  ];

  constructor(basePath: string) {
    this.basePath = basePath;
    this.loadState();
  }

  // Calculate checksum for a file
  private calculateChecksum(filePath: string): string {
    try {
      // Read file content
      const fileBuffer = fs.readFileSync(filePath);

      // Get file stats for additional metadata
      const stats = fs.statSync(filePath);

      // Create a hash that includes both content and size
      // This helps detect changes even if the content hash might be the same
      const hashSum = crypto.createHash('md5');
      hashSum.update(fileBuffer);

      // Add file size and modification time to the hash
      // This ensures we detect changes even if the content hash is the same
      hashSum.update(String(stats.size));
      hashSum.update(String(stats.mtimeMs));

      return hashSum.digest('hex');
    } catch (error) {
      console.error(`Error calculating checksum for ${filePath}:`, error);
      return 'error';
    }
  }

  // Check if a path should be ignored
  private shouldIgnore(filePath: string): boolean {
    // Normalize the path for consistent comparison (especially on Windows)
    const normalizedPath = filePath.replace(/\\/g, '/');
    const relativePath = path.relative(this.basePath, filePath).replace(/\\/g, '/');

    // Explicitly check for the file tracker state file
    if (relativePath === 'server/data/file-tracker-state.json') {
      console.log(`Ignoring file tracker state file: ${relativePath}`);
      return true;
    }

    // Check against ignore patterns
    return this.ignorePatterns.some(pattern => {
      if (pattern.startsWith('*')) {
        // Handle wildcard patterns like *.log
        const extension = pattern.substring(1);
        return normalizedPath.toLowerCase().endsWith(extension.toLowerCase());
      }

      // Convert pattern to use forward slashes for consistency
      const normalizedPattern = pattern.replace(/\\/g, '/');

      // Case-insensitive includes check for Windows compatibility
      return relativePath.toLowerCase().includes(normalizedPattern.toLowerCase());
    });
  }

  // Scan all files and update records
  public scanFiles(forceRescan: boolean = false): FileRecord[] {
    console.log(`Scanning files with forceRescan=${forceRescan}`);

    if (forceRescan) {
      // For a force rescan, we'll delete the state file and clear the in-memory records
      console.log('Force rescan requested - clearing current file records');

      try {
        // Delete the state file if it exists
        const stateFile = path.join(this.basePath, 'server', 'data', 'file-tracker-state.json');
        if (fs.existsSync(stateFile)) {
          fs.unlinkSync(stateFile);
          console.log(`Deleted file tracker state file for force rescan: ${stateFile}`);
        }
      } catch (error) {
        console.error('Error deleting file tracker state file during force rescan:', error);
      }

      // Clear the in-memory records
      this.fileRecords.clear();

      // When doing a force rescan, all files will be treated as "new"
      console.log('All files will be treated as new during force rescan');

      // Scan all files and mark them as changed
      const allFiles: FileRecord[] = [];

      // Recursive function to scan directories
      const scanDir = (dirPath: string) => {
        try {
          const entries = fs.readdirSync(dirPath, { withFileTypes: true });

          for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            const relativePath = path.relative(this.basePath, fullPath).replace(/\\/g, '/');

            // Skip ignored files and directories
            if (this.shouldIgnore(fullPath)) {
              continue;
            }

            if (entry.isDirectory()) {
              scanDir(fullPath);
            } else {
              try {
                const stats = fs.statSync(fullPath);
                const checksum = this.calculateChecksum(fullPath);

                // Add to the list of changed files
                allFiles.push({
                  path: relativePath,
                  checksum,
                  lastModified: stats.mtime,
                  status: 'modified' // Mark all files as modified for force rescan
                });

                // Update the file records
                this.fileRecords.set(relativePath, {
                  path: relativePath,
                  checksum,
                  lastModified: stats.mtime
                });
              } catch (error) {
                console.error(`Error processing file ${fullPath}:`, error);
              }
            }
          }
        } catch (error) {
          console.error(`Error scanning directory ${dirPath}:`, error);
        }
      };

      scanDir(this.basePath);

      // Save the state
      this.saveState();

      console.log(`Force rescan complete. Found ${allFiles.length} files.`);
      return allFiles;
    } else {
      // Normal scan - use getChangedFiles with updateState=true
      const changedFiles = this.getChangedFiles(true);
      console.log(`Scan complete. Found ${changedFiles.length} changed files.`);
      return changedFiles;
    }
  }

  // Get all changed files since last scan
  public getChangedFiles(updateState: boolean = false): FileRecord[] {
    console.log(`Getting changed files with updateState=${updateState}`);
    const changedFiles: FileRecord[] = [];
    const currentRecords = new Map<string, FileRecord>();

    // Count of files processed
    let filesProcessed = 0;
    let filesIgnored = 0;
    let filesModified = 0;
    let filesNew = 0;
    let filesDeleted = 0;

    // Recursive function to scan directories
    const scanDir = (dirPath: string) => {
      try {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);
          const relativePath = path.relative(this.basePath, fullPath).replace(/\\/g, '/');

          // Skip ignored files and directories
          if (this.shouldIgnore(fullPath)) {
            filesIgnored++;
            if (relativePath === 'server/data/file-tracker-state.json') {
              console.log(`Ignoring file tracker state file: ${relativePath}`);
            }
            continue;
          }

          if (entry.isDirectory()) {
            scanDir(fullPath);
          } else {
            filesProcessed++;

            try {
              const stats = fs.statSync(fullPath);
              const checksum = this.calculateChecksum(fullPath);

              const fileRecord = {
                path: relativePath,
                checksum,
                lastModified: stats.mtime
              };

              currentRecords.set(relativePath, fileRecord);

              // Check if file is new or modified
              const oldRecord = this.fileRecords.get(relativePath);

              if (!oldRecord) {
                // New file
                filesNew++;
                changedFiles.push({
                  ...fileRecord,
                  status: 'new'
                });
                console.log(`New file detected: ${relativePath}`);
              } else {
                // Check if file has been modified by comparing checksums
                // We use checksums instead of timestamps because some file systems
                // might not update timestamps accurately
                if (oldRecord.checksum !== checksum) {
                  filesModified++;
                  changedFiles.push({
                    ...fileRecord,
                    status: 'modified'
                  });
                  console.log(`Modified file detected: ${relativePath} (checksum changed)`);
                } else if (updateState === false) {
                  // If we're just checking for changes (not updating state),
                  // include unchanged files in the result with status 'unchanged'
                  // This helps with debugging and ensures we have a complete list
                  changedFiles.push({
                    ...fileRecord,
                    status: 'unchanged'
                  });
                }
              }
            } catch (error) {
              console.error(`Error processing file ${fullPath}:`, error);
            }
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error);
      }
    };

    scanDir(this.basePath);

    // Check for deleted files
    this.fileRecords.forEach((_, filePath) => {
      if (!currentRecords.has(filePath)) {
        // File was deleted
        filesDeleted++;
        changedFiles.push({
          path: filePath,
          checksum: 'DELETED',
          lastModified: new Date(),
          status: 'deleted'
        });
        console.log(`Deleted file detected: ${filePath}`);
      }
    });

    console.log(`Scan complete. Processed ${filesProcessed} files, ignored ${filesIgnored} files.`);
    console.log(`Changes detected: ${filesNew} new, ${filesModified} modified, ${filesDeleted} deleted`);

    // Update the internal state if requested
    if (updateState) {
      // Only update state if we're doing a full update
      this.fileRecords = currentRecords;
      this.saveState();
      console.log(`Updated file tracker state with ${currentRecords.size} files`);
    }

    return changedFiles;
  }

  // Save the current state
  public saveState(): void {
    try {
      const stateDir = path.join(this.basePath, 'server', 'data');

      // Create directory if it doesn't exist
      if (!fs.existsSync(stateDir)) {
        fs.mkdirSync(stateDir, { recursive: true });
      }

      const stateFile = path.join(stateDir, 'file-tracker-state.json');

      // Convert Map to array for serialization
      const recordsArray = Array.from(this.fileRecords.entries()).map(([key, value]) => ({
        path: key,
        checksum: value.checksum,
        lastModified: value.lastModified.toISOString()
      }));

      fs.writeFileSync(stateFile, JSON.stringify(recordsArray, null, 2));
    } catch (error) {
      console.error('Error saving file tracker state:', error);
    }
  }

  // Load the previous state
  private loadState(): void {
    try {
      const stateFile = path.join(this.basePath, 'server', 'data', 'file-tracker-state.json');

      if (fs.existsSync(stateFile)) {
        const stateData = fs.readFileSync(stateFile, 'utf8');
        const recordsArray = JSON.parse(stateData);

        this.fileRecords.clear();

        for (const record of recordsArray) {
          this.fileRecords.set(record.path, {
            path: record.path,
            checksum: record.checksum,
            lastModified: new Date(record.lastModified)
          });
        }
      }
    } catch (error) {
      console.error('Error loading file tracker state:', error);
      // If there's an error loading the state, we'll start fresh
      this.fileRecords.clear();
    }
  }

  // Reset the file tracker state
  public resetState(): void {
    this.fileRecords.clear();

    try {
      // Delete the state file if it exists
      const stateFile = path.join(this.basePath, 'server', 'data', 'file-tracker-state.json');
      if (fs.existsSync(stateFile)) {
        fs.unlinkSync(stateFile);
        console.log(`Deleted file tracker state file: ${stateFile}`);
      }
    } catch (error) {
      console.error('Error deleting file tracker state file:', error);
      // Still save an empty state even if deletion fails
      this.saveState();
    }
  }
}

export { FileTracker };
