// Script to create test orders
import fetch from 'node-fetch';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createTestOrders() {
  try {
    console.log('Creating test orders...');

    // Login to get session
    console.log('Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('Login successful');

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');

    // Create a regular order
    console.log('Creating regular order...');
    const regularOrder = {
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '19.99',
      status: 'paid',
      country: 'United States',
      appType: 'IPTV Smarters Pro',
      createdAt: new Date().toISOString(),
      isTrialOrder: false,
      hasUpgraded: false,
      notes: 'Default regular order customer'
    };

    const regularOrderResponse = await fetch('http://localhost:3001/api/admin/invoices', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify(regularOrder),
    });

    if (!regularOrderResponse.ok) {
      throw new Error(`Failed to create regular order: ${regularOrderResponse.statusText}`);
    }

    const regularOrderData = await regularOrderResponse.json();
    console.log('Regular order created:', regularOrderData);

    // Create a trial order
    console.log('Creating trial order...');
    const trialOrder = {
      customerName: 'Jane Doe',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '4.99',
      status: 'paid',
      country: 'United Kingdom',
      appType: 'MAG',
      macAddress: '00:1A:79:B4:E7:2D',
      createdAt: new Date().toISOString(),
      isTrialOrder: true,
      hasUpgraded: false,
      notes: 'Default trial order customer'
    };

    const trialOrderResponse = await fetch('http://localhost:3001/api/admin/invoices', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify(trialOrder),
    });

    if (!trialOrderResponse.ok) {
      throw new Error(`Failed to create trial order: ${trialOrderResponse.statusText}`);
    }

    const trialOrderData = await trialOrderResponse.json();
    console.log('Trial order created:', trialOrderData);

    console.log('Test orders created successfully!');
    console.log('\nRegular Order Details:');
    console.log('- Customer: John Smith (<EMAIL>)');
    console.log('- Country: United States');
    console.log('- Application: IPTV Smarters Pro');
    console.log('- Amount: $19.99');
    console.log('- Status: paid');

    console.log('\nTrial Order Details:');
    console.log('- Customer: Jane Doe (<EMAIL>)');
    console.log('- Country: United Kingdom');
    console.log('- Application: MAG');
    console.log('- MAC Address: 00:1A:79:B4:E7:2D');
    console.log('- Amount: $4.99');
    console.log('- Status: paid');
  } catch (error) {
    console.error('Error creating test orders:', error);
  }
}

createTestOrders();
