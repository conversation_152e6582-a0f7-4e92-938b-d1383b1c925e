import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  url: string,
  method: string = 'GET',
  data?: unknown | undefined,
): Promise<any> {
  console.log(`API Request: ${method} ${url}`, data);

  const options = {
    method,
    headers: data ? {
      "Content-Type": "application/json",
    } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include" as RequestCredentials, // This ensures cookies are sent with the request
  };

  console.log('Request options:', options);

  try {
    console.log(`Sending fetch request to ${url}...`);
    const res = await fetch(url, options);
    console.log(`Received response from ${url}`);

    console.log(`Response status: ${res.status} ${res.statusText}`);
    console.log('Response headers:', Object.fromEntries([...res.headers.entries()]));

    if (!res.ok) {
      let errorText;
      try {
        errorText = await res.text();
        console.error(`API Error: ${res.status} ${res.statusText}`, errorText);

        // Try to parse the error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.message) {
            throw new Error(errorJson.message);
          }
        } catch (jsonError) {
          // Not JSON, continue with text
        }

        // Try to parse the error as HTML to extract more information
        if (errorText.includes('<!DOCTYPE html>')) {
          console.error('Received HTML error response:', errorText);
        }
      } catch (textError) {
        errorText = res.statusText;
      }

      throw new Error(errorText || res.statusText);
    }

    console.log(`Parsing response from ${url} as JSON...`);
    const responseData = await res.json();
    console.log('Response data:', responseData);
    return responseData;
  } catch (error) {
    console.error('API Request failed:', error);
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    try {
      return await apiRequest(queryKey[0] as string, 'GET');
    } catch (error: any) {
      if (unauthorizedBehavior === "returnNull" && error.message?.includes('401')) {
        return null;
      }
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
