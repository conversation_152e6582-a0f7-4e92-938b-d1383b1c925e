import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addLogoThemeToCheckout() {
  console.log('Adding header_logo, footer_logo, and theme_mode columns to custom_checkout_pages...');

  try {
    // Add header_logo column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN header_logo TEXT
    `);

    // Add footer_logo column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN footer_logo TEXT
    `);

    // Add theme_mode column with default value
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN theme_mode TEXT NOT NULL DEFAULT 'light'
    `);

    console.log('Successfully added header_logo, footer_logo, and theme_mode columns to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding columns to custom_checkout_pages:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addLogoThemeToCheckout()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
