import { apiRequest } from '@/lib/queryClient';

// Types
export interface DefaultTestCustomer {
  enabled: boolean;
  name: string;
  email: string;
}

export interface EmailDomainRestriction {
  enabled: boolean;
  allowedDomains: string;
}

export interface GeneralSettings {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  enableCheckout: boolean;
  enableCustomCheckout: boolean;
  enableTestMode: boolean;
  defaultTestCustomer: DefaultTestCustomer;
  emailDomainRestriction: EmailDomainRestriction;
}

// API functions
export const getGeneralSettings = async (): Promise<GeneralSettings> => {
  return apiRequest('/api/general-settings', 'GET');
};

export const updateGeneralSettings = async (settings: GeneralSettings): Promise<GeneralSettings> => {
  return apiRequest('/api/general-settings', 'PUT', settings);
};

export const getAllowedEmailDomains = async (): Promise<{ enabled: boolean; domains: string[] }> => {
  return apiRequest('/api/general-settings/allowed-email-domains', 'GET');
};

export const getDefaultTestCustomer = async (): Promise<DefaultTestCustomer> => {
  return apiRequest('/api/general-settings/default-test-customer', 'GET');
};
