import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addHeaderFooterToCheckout() {
  console.log('Adding header_title and footer_text columns to custom_checkout_pages...');

  try {
    // Add header_title column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN header_title TEXT
    `);

    // Add footer_text column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN footer_text TEXT
    `);

    console.log('Successfully added header_title and footer_text columns to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding columns to custom_checkout_pages:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addHeaderFooterToCheckout()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
