{"metadata": {"id": "14da2bd3-c33e-42d9-8cec-6490913ebed3", "name": "hhhh", "date": "2025-05-18T12:09:38.237Z", "type": "full-backup"}, "data": {"products": [{"name": "Premium WordPress Theme", "description": "A professionally designed WordPress theme with premium features for blogs and business websites.", "price": "49.99", "imageUrl": "https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 1}, {"name": "Social Media Marketing eBook", "description": "Comprehensive guide to modern social media marketing strategies for small businesses and entrepreneurs.", "price": "19.99", "imageUrl": "https://images.unsplash.com/photo-1611926653458-09294b3142bf?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 2}, {"name": "Productivity App Template", "description": "Ready-to-use template for productivity mobile apps with custom UI components and animations.", "price": "79.99", "imageUrl": "https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 3}, {"name": "Stock Photo Collection", "description": "50+ high resolution professionally shot stock photos for commercial use, perfect for websites and marketing.", "price": "39.99", "imageUrl": "https://images.unsplash.com/photo-1542038784456-1ea8e935640e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 4}, {"name": "Online Course Bundle", "description": "Complete bundle of 5 online courses covering web development, design, and digital marketing fundamentals.", "price": "129.99", "imageUrl": "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 5}, {"name": "UI/UX Design System", "description": "Comprehensive UI/UX design system with over 200 components for Figma, Sketch, and Adobe XD.", "price": "89.99", "imageUrl": "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60", "active": true, "id": 6}], "invoices": [], "customCheckout": [], "allowedUsernames": [{"username": "test_user1", "notes": "Test username", "createdAt": "2025-05-18T12:08:25.480Z", "id": 1}, {"username": "test_user2", "notes": "Test username", "createdAt": "2025-05-18T12:08:25.483Z", "id": 2}, {"username": "test_user3", "notes": "Test username", "createdAt": "2025-05-18T12:08:25.483Z", "id": 3}], "emailTemplates": [], "paypalButtons": [{"name": "Basic PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"SAMPLE123456\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "Standard PayPal Buy Now button", "active": true, "createdAt": "2025-05-18T12:08:25.484Z", "updatedAt": "2025-05-18T12:08:25.484Z", "id": 1}, {"name": "Premium PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"PREMIUM789012\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "Premium PayPal Pay Now button", "active": true, "createdAt": "2025-05-18T12:08:25.484Z", "updatedAt": "2025-05-18T12:08:25.484Z", "id": 2}, {"name": "Subscription PayPal Button", "buttonCode": "<form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_top\">\n<input type=\"hidden\" name=\"cmd\" value=\"_s-xclick\">\n<input type=\"hidden\" name=\"hosted_button_id\" value=\"SUB345678\">\n<input type=\"image\" src=\"https://www.paypalobjects.com/en_US/i/btn/btn_subscribeCC_LG.gif\" border=\"0\" name=\"submit\" alt=\"PayPal - The safer, easier way to pay online!\">\n<img alt=\"\" border=\"0\" src=\"https://www.paypalobjects.com/en_US/i/scr/pixel.gif\" width=\"1\" height=\"1\">\n</form>", "description": "PayPal Subscription button", "active": true, "createdAt": "2025-05-18T12:08:25.484Z", "updatedAt": "2025-05-18T12:08:25.484Z", "id": 3}], "settings": {"general": {"siteName": "PayPal Invoicer", "siteDescription": "Generate and manage PayPal invoices", "logoUrl": "", "faviconUrl": "", "primaryColor": "#0070ba", "secondaryColor": "#003087", "footerText": "© 2023 PayPal Invoicer", "enableCheckout": true, "enableCustomCheckout": true, "enableTestMode": true, "defaultTestCustomer": {"enabled": true, "name": "Test Customer", "email": "<EMAIL>"}, "emailDomainRestriction": {"enabled": true, "allowedDomains": "gmail.com, hotmail.com, yahoo.com"}}, "email": {"providers": [{"id": "smtp-1", "name": "Primary SMTP", "active": true, "isDefault": true, "isBackup": false, "credentials": {"host": "smtp-relay.example.com", "port": "587", "secure": false, "auth": {"user": "<EMAIL>", "pass": "********"}, "fromEmail": "<EMAIL>", "fromName": "PayPal Invoicer"}}]}, "payment": {"providers": [{"id": "paypal", "name": "PayPal", "active": true, "config": {"clientId": "********", "clientSecret": "********", "mode": "sandbox", "webhookId": "", "paypalEmail": "<EMAIL>"}}]}}}}