import { CheckoutData, Product } from "@shared/schema";
import { getPaymentConfig } from "../config-storage";
import checkoutSdk from '@paypal/checkout-server-sdk';

// PayPal environment setup
function getPayPalClient(clientId: string, clientSecret: string, mode: string) {
  const environment = mode === 'sandbox'
    ? new checkoutSdk.core.SandboxEnvironment(clientId, clientSecret)
    : new checkoutSdk.core.LiveEnvironment(clientId, clientSecret);

  return new checkoutSdk.core.PayPalHttpClient(environment);
}

interface PayPalInvoiceResponse {
  id: string; // PayPal invoice ID
  status: string;
  detail: {
    invoice_number: string;
    reference: string;
    currency_code: string;
    note: string;
    payment_term: {
      term_type: string;
      due_date: string;
    };
    metadata: {
      create_time: string;
      recipient_view_url: string;
      invoicer_view_url: string;
    };
  };
  amount: {
    currency_code: string;
    value: string;
  };
  invoicer: {
    name: {
      given_name: string;
      surname: string;
    };
    email_address: string;
  };
  primary_recipients: Array<{
    billing_info: {
      name: {
        given_name: string;
        surname: string;
      };
      email_address: string;
    };
  }>;
  items: Array<{
    name: string;
    description: string;
    quantity: string;
    unit_amount: {
      currency_code: string;
      value: string;
    };
  }>;
}

export async function generatePayPalInvoice(customerData: CheckoutData, product: Product): Promise<{ id: string, url: string }> {
  try {
    // Get the PayPal settings from the configuration
    const paymentConfig = getPaymentConfig();
    const paypalProvider = paymentConfig.providers.find((p: any) => p.id === 'paypal' && p.active);

    if (!paypalProvider) {
      console.warn('No active PayPal provider configured');
      throw new Error('PayPal is not configured or not active');
    }

    const { config } = paypalProvider;

    if (!config.clientId || !config.clientSecret) {
      throw new Error('PayPal client ID and client secret are required');
    }

    console.log('Generating PayPal invoice with settings:', {
      mode: config.mode,
      clientIdLength: config.clientId.length,
      hasSecret: Boolean(config.clientSecret),
      customer: customerData.email
    });

    // Create PayPal client with the configured credentials
    const client = getPayPalClient(config.clientId, config.clientSecret, config.mode);

    try {
      // Create a PayPal order that looks like an invoice
      const request = new checkoutSdk.orders.OrdersCreateRequest();
      request.prefer("return=representation");

      // Calculate the item total (price * quantity)
      const itemTotal = product.price.toString();

      // Generate a unique invoice ID
      const invoiceId = `INV-${Date.now()}`;

      request.requestBody({
        intent: 'CAPTURE',
        purchase_units: [{
          amount: {
            currency_code: 'USD',
            value: itemTotal,
            breakdown: {
              item_total: {
                currency_code: 'USD',
                value: itemTotal
              }
            }
          },
          description: `Invoice: ${invoiceId} - ${product.name}`,
          custom_id: `ORDER-${product.id}`,
          invoice_id: invoiceId,
          items: [{
            name: product.name,
            description: product.description || 'Product purchase',
            quantity: '1',
            unit_amount: {
              currency_code: 'USD',
              value: itemTotal
            }
          }]
        }],
        application_context: {
          brand_name: 'PayPal Invoicer',
          shipping_preference: 'NO_SHIPPING',
          user_action: 'PAY_NOW',
          return_url: `http://localhost:3002/payment-success?email=${encodeURIComponent(customerData.email)}&invoice=${invoiceId}`,
          cancel_url: 'http://localhost:3002/payment-cancel'
        }
      });

      // Execute the request
      const response = await client.execute(request);

      // Get the approval URL (this is what the customer will use to pay)
      const approvalUrl = response.result.links.find(link => link.rel === 'approve')?.href;

      if (!approvalUrl) {
        throw new Error('Could not get approval URL from PayPal response');
      }

      return {
        id: invoiceId,
        url: approvalUrl
      };
    } catch (error) {
      console.error('Error creating PayPal invoice:', error);

      // Fallback to the simulated invoice if there's an error with the PayPal API
      console.log('Falling back to simulated invoice due to API error');
      const invoiceId = `INV-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
      const encodedEmail = encodeURIComponent(customerData.email);
      const invoiceUrl = `https://www.sandbox.paypal.com/invoice/p/${invoiceId}?recipient=${encodedEmail}`;

      return {
        id: invoiceId,
        url: invoiceUrl
      };
    }

    /*
    The real implementation would look something like this:

    const paypalClient = new PayPalClient({
      clientId: process.env.PAYPAL_CLIENT_ID,
      clientSecret: process.env.PAYPAL_CLIENT_SECRET,
      environment: 'sandbox' // or 'live' for production
    });

    const invoiceRequest = {
      detail: {
        invoice_number: `INV-${Date.now()}`,
        reference: `ORDER-${product.id}`,
        currency_code: "USD",
        note: "Thank you for your purchase!",
        payment_term: {
          term_type: "DUE_ON_RECEIPT"
        }
      },
      invoicer: {
        name: {
          given_name: "Your",
          surname: "Business"
        },
        email_address: "<EMAIL>"
      },
      primary_recipients: [
        {
          billing_info: {
            name: {
              given_name: customerData.fullName.split(' ')[0] || customerData.fullName,
              surname: customerData.fullName.split(' ').slice(1).join(' ') || ' '
            },
            email_address: customerData.email
          }
        }
      ],
      items: [
        {
          name: product.name,
          description: product.description,
          quantity: "1",
          unit_amount: {
            currency_code: "USD",
            value: product.price
          }
        }
      ]
    };

    const invoice = await paypalClient.invoice.create(invoiceRequest);
    await paypalClient.invoice.send(invoice.id);

    return {
      id: invoice.id,
      url: invoice.metadata.recipient_view_url
    };
    */
  } catch (error) {
    console.error("Error generating PayPal invoice:", error);
    throw error;
  }
}

// Add a function to test PayPal connection
export async function testPayPalConnection(config: any): Promise<boolean> {
  try {
    // Create a client with the provided credentials
    const client = getPayPalClient(config.clientId, config.clientSecret, config.mode);

    // Make a simple API call to verify the credentials
    const request = new checkoutSdk.orders.OrdersCreateRequest();

    // Include the required breakdown with item_total
    const itemTotal = '0.01';

    request.requestBody({
      intent: 'CAPTURE',
      purchase_units: [{
        amount: {
          currency_code: 'USD',
          value: itemTotal,
          breakdown: {
            item_total: {
              currency_code: 'USD',
              value: itemTotal
            }
          }
        },
        items: [{
          name: 'Test Item',
          quantity: '1',
          unit_amount: {
            currency_code: 'USD',
            value: itemTotal
          }
        }]
      }]
    });

    await client.execute(request);
    return true;
  } catch (error) {
    console.error('PayPal connection test failed:', error);
    return false;
  }
}
