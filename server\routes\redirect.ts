import express from 'express';
import crypto from 'crypto';

const router = express.Router();

// Encryption key for tokens (in production, use environment variable)
const ENCRYPTION_KEY = process.env.REDIRECT_ENCRYPTION_KEY || 'your-32-character-secret-key-here';
const ALGORITHM = 'aes-256-cbc';

// Encrypt data for secure token
function encryptData(data: any): string {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

// Decrypt token data
function decryptData(token: string): any {
  try {
    const [ivHex, encrypted] = token.split(':');
    const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  } catch (error) {
    throw new Error('Invalid token');
  }
}

// Generate redirect page HTML
function generateRedirectPage(targetUrl: string, fakeReferrer: string, delay: number): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="referrer" content="no-referrer">
  <meta http-equiv="refresh" content="${delay / 1000};url=${targetUrl}">
  <title>Redirecting...</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    .spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    h1 {
      margin: 0 0 0.5rem;
      font-size: 1.5rem;
      font-weight: 600;
    }
    p {
      margin: 0;
      opacity: 0.8;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="spinner"></div>
    <h1>Loading...</h1>
    <p>Please wait while we redirect you securely</p>
  </div>

  <script>
    // Override document.referrer before redirect
    try {
      Object.defineProperty(document, 'referrer', {
        value: '${fakeReferrer}',
        writable: false,
        configurable: false
      });
    } catch (e) {
      // Fallback for browsers that don't allow referrer override
      console.log('Referrer override not supported');
    }

    // Additional referrer masking techniques
    if (window.history && window.history.replaceState) {
      window.history.replaceState(null, '', '${fakeReferrer}');
    }

    // Redirect after delay
    setTimeout(function() {
      // Use location.replace to avoid adding to history
      window.location.replace('${targetUrl}');
    }, ${delay});

    // Fallback redirect in case setTimeout fails
    setTimeout(function() {
      if (window.location.href.indexOf('${targetUrl}') === -1) {
        window.location.href = '${targetUrl}';
      }
    }, ${delay + 1000});
  </script>
</body>
</html>
  `;
}

// Generate masked link (simple URL parameters)
router.get('/generate', (req, res) => {
  try {
    const { slug, referrer, delay } = req.query;

    if (!slug) {
      return res.status(400).send('Checkout slug is required');
    }

    const targetUrl = `${req.protocol}://${req.get('host')}/checkout/${slug}`;
    const fakeReferrer = referrer || 'https://facebook.com';
    const redirectDelay = parseInt(delay as string) || 2000;

    const tokenData = {
      targetUrl,
      fakeReferrer,
      delay: redirectDelay,
      timestamp: Date.now()
    };

    const token = encryptData(tokenData);

    // Redirect to the masked redirect page
    res.redirect(`/api/redirect/${token}`);
  } catch (error) {
    console.error('Error generating masked link:', error);
    res.status(500).send('Failed to generate masked link');
  }
});

// Generate masked link (POST method for API)
router.post('/generate-link', (req, res) => {
  try {
    const { checkoutSlug, fakeReferrer, delay } = req.body;

    if (!checkoutSlug) {
      return res.status(400).json({ error: 'Checkout slug is required' });
    }

    const targetUrl = `${req.protocol}://${req.get('host')}/checkout/${checkoutSlug}`;

    const tokenData = {
      targetUrl,
      fakeReferrer: fakeReferrer || 'https://facebook.com',
      delay: delay || 2000,
      timestamp: Date.now()
    };

    const token = encryptData(tokenData);
    const maskedUrl = `${req.protocol}://${req.get('host')}/api/redirect/${token}`;

    res.json({
      success: true,
      maskedUrl,
      originalUrl: targetUrl,
      fakeReferrer: tokenData.fakeReferrer,
      delay: tokenData.delay
    });
  } catch (error) {
    console.error('Error generating masked link:', error);
    res.status(500).json({ error: 'Failed to generate masked link' });
  }
});

// Handle redirect
router.get('/:token', (req, res) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).send('Invalid redirect token');
    }

    const data = decryptData(token);

    // Check if token is not too old (optional security measure)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    if (Date.now() - data.timestamp > maxAge) {
      return res.status(400).send('Redirect link has expired');
    }

    const html = generateRedirectPage(data.targetUrl, data.fakeReferrer, data.delay);

    // Set headers to prevent referrer leakage
    res.setHeader('Referrer-Policy', 'no-referrer');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.send(html);
  } catch (error) {
    console.error('Error handling redirect:', error);
    res.status(400).send('Invalid or expired redirect token');
  }
});

export default router;
