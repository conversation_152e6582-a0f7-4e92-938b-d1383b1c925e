import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useLocation } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Validation schema for enabling 2FA
const enableTwoFactorSchema = z.object({
  token: z.string().min(6, 'Verification code must be at least 6 characters').max(8, 'Verification code must be at most 8 characters'),
});

type EnableTwoFactorFormData = z.infer<typeof enableTwoFactorSchema>;

export default function TwoFactorSetup() {
  const { toast } = useToast();
  const { checkAuth } = useAdminAuth();
  const [, setLocation] = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [secret, setSecret] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  // Form setup
  const form = useForm<EnableTwoFactorFormData>({
    resolver: zodResolver(enableTwoFactorSchema),
    defaultValues: {
      token: '',
    },
  });

  // Load 2FA status
  useEffect(() => {
    const loadTwoFactorStatus = async () => {
      try {
        setIsLoading(true);
        const response = await apiRequest('/api/admin/two-factor/status', 'GET');
        setIsEnabled(response.enabled);
      } catch (error) {
        console.error('Error loading 2FA status:', error);
        toast({
          title: "Failed to load 2FA status",
          description: error instanceof Error ? error.message : "An error occurred. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadTwoFactorStatus();
  }, [toast]);

  // Start 2FA setup
  const startSetup = async () => {
    try {
      setIsLoading(true);
      const response = await apiRequest('/api/admin/two-factor/setup', 'POST');
      setSecret(response.secret);
      setQrCodeUrl(response.qrCodeUrl);
      setIsSetupMode(true);
    } catch (error) {
      console.error('Error setting up 2FA:', error);
      toast({
        title: "Failed to set up 2FA",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Disable 2FA
  const disableTwoFactor = async () => {
    try {
      setIsSubmitting(true);
      await apiRequest('/api/admin/two-factor/disable', 'POST');
      setIsEnabled(false);
      toast({
        title: "Two-factor authentication disabled",
        description: "Two-factor authentication has been disabled for your account.",
      });

      // Refresh auth state and redirect to dashboard
      checkAuth();

      // Add a small delay before redirecting
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 1500);
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      toast({
        title: "Failed to disable 2FA",
        description: error instanceof Error ? error.message : "An error occurred. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit handler for enabling 2FA
  const onSubmit = async (data: EnableTwoFactorFormData) => {
    setIsSubmitting(true);
    try {
      await apiRequest('/api/admin/two-factor/enable', 'POST', {
        secret,
        token: data.token
      });
      setIsEnabled(true);
      setIsSetupMode(false);
      toast({
        title: "Two-factor authentication enabled",
        description: "Two-factor authentication has been enabled for your account.",
      });
      form.reset();

      // Refresh auth state and redirect to dashboard
      checkAuth();

      // Add a small delay before redirecting
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 1500);
    } catch (error) {
      toast({
        title: "Failed to enable 2FA",
        description: error instanceof Error ? error.message : "Invalid verification code. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel setup
  const cancelSetup = () => {
    setIsSetupMode(false);
    form.reset();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p>Loading two-factor authentication status...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isSetupMode) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Set Up Two-Factor Authentication</CardTitle>
          <CardDescription>
            Scan the QR code with your authenticator app and enter the verification code to enable 2FA.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="border p-4 rounded-lg">
              <img src={qrCodeUrl} alt="QR Code for 2FA" className="w-48 h-48" />
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-2">If you can't scan the QR code, enter this code manually:</p>
              <code className="bg-muted p-2 rounded text-sm font-mono">{secret}</code>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="token"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Verification Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter code from your authenticator app"
                        {...field}
                        autoComplete="one-time-code"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelSetup}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    "Enable Two-Factor Authentication"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Two-Factor Authentication</CardTitle>
        <CardDescription>
          Add an extra layer of security to your account by requiring a verification code from your authenticator app.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isEnabled ? (
          <Alert className="mb-6">
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>Two-factor authentication is enabled</AlertTitle>
            <AlertDescription>
              Your account is protected with two-factor authentication. You will need to enter a verification code from your authenticator app when logging in.
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="mb-6" variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Two-factor authentication is not enabled</AlertTitle>
            <AlertDescription>
              Your account is not protected with two-factor authentication. We strongly recommend enabling this feature for additional security.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {isEnabled ? (
          <Button
            variant="destructive"
            onClick={disableTwoFactor}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Disabling...
              </>
            ) : (
              "Disable Two-Factor Authentication"
            )}
          </Button>
        ) : (
          <Button
            onClick={startSetup}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting up...
              </>
            ) : (
              "Set Up Two-Factor Authentication"
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
