// <PERSON>ript to create default checkout pages
import fetch from 'node-fetch';

async function createCheckoutPages() {
  try {
    // Login to get session
    console.log('Logging in...');
    const loginResponse = await fetch('http://localhost:3001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('Login successful:', loginData);

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');

    // Create trial checkout page
    console.log('Creating trial checkout page...');
    const trialCheckoutPage = {
      title: 'Default Trial Checkout',
      productName: 'IPTV Trial Subscription',
      productDescription: 'Try our premium IPTV service for 24 hours with full access to all channels and features.',
      price: 4.99,
      paymentMethod: 'trial-paypal-button-embed',
      trialPaypalButtonId: 'trial-button-1',
      smtpProviderId: 'smtp-1',
      isTrialCheckout: true,
      active: true,
    };

    const trialResponse = await fetch('http://localhost:3001/api/custom-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify(trialCheckoutPage),
    });

    if (!trialResponse.ok) {
      throw new Error(`Failed to create trial checkout page: ${trialResponse.statusText}`);
    }

    const trialData = await trialResponse.json();
    console.log('Trial checkout page created:', trialData);

    // Create regular checkout page
    console.log('Creating regular checkout page...');
    const regularCheckoutPage = {
      title: 'Default Regular Checkout',
      productName: 'IPTV Premium Subscription',
      productDescription: 'Get access to our premium IPTV service with over 10,000 channels, VOD, and more.',
      price: 19.99,
      paymentMethod: 'paypal-button-embed',
      paypalButtonId: 'button-1',
      smtpProviderId: 'smtp-1',
      isTrialCheckout: false,
      active: true,
    };

    const regularResponse = await fetch('http://localhost:3001/api/custom-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
      },
      body: JSON.stringify(regularCheckoutPage),
    });

    if (!regularResponse.ok) {
      throw new Error(`Failed to create regular checkout page: ${regularResponse.statusText}`);
    }

    const regularData = await regularResponse.json();
    console.log('Regular checkout page created:', regularData);

    console.log('Default checkout pages created successfully!');
  } catch (error) {
    console.error('Error creating checkout pages:', error);
  }
}

createCheckoutPages();
