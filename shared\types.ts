import { User as BaseUser } from './schema';

// Device interface
export interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

// Recovery code interface
export interface RecoveryCode {
  code: string;
  used: boolean;
}

// Extended User interface
export interface User extends BaseUser {
  email?: string;
  rememberMe?: boolean;
  resetToken?: string;
  resetTokenExpiry?: string;
  twoFactorSecret?: string;
  twoFactorEnabled: boolean;
  recoveryCodes: RecoveryCode[];
  devices: Device[];
}

// Re-export types from schema
export {
  Product,
  Invoice,
  CustomCheckoutPage,
  AllowedEmail,
  EmailTemplate,
  PaypalButton,
  CustomInvoice
} from './schema';
