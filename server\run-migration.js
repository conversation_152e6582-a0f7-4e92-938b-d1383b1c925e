import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import { sql } from 'drizzle-orm';

console.log('Starting migration...');

// Initialize database
const sqlite = new Database('data.db');
const db = drizzle(sqlite);

async function addLogoThemeToCheckout() {
  console.log('Adding header_logo, footer_logo, and theme_mode columns to custom_checkout_pages...');

  try {
    // Check if columns already exist
    const tableInfo = db.all(sql`PRAGMA table_info(custom_checkout_pages)`);
    const existingColumns = tableInfo.map(col => col.name);

    console.log('Existing columns:', existingColumns);

    // Add header_logo column if it doesn't exist
    if (!existingColumns.includes('header_logo')) {
      await db.run(sql`
        ALTER TABLE custom_checkout_pages
        ADD COLUMN header_logo TEXT
      `);
      console.log('Added header_logo column');
    } else {
      console.log('header_logo column already exists');
    }

    // Add footer_logo column if it doesn't exist
    if (!existingColumns.includes('footer_logo')) {
      await db.run(sql`
        ALTER TABLE custom_checkout_pages
        ADD COLUMN footer_logo TEXT
      `);
      console.log('Added footer_logo column');
    } else {
      console.log('footer_logo column already exists');
    }

    // Add theme_mode column if it doesn't exist
    if (!existingColumns.includes('theme_mode')) {
      await db.run(sql`
        ALTER TABLE custom_checkout_pages
        ADD COLUMN theme_mode TEXT NOT NULL DEFAULT 'light'
      `);
      console.log('Added theme_mode column');
    } else {
      console.log('theme_mode column already exists');
    }

    console.log('Successfully added header_logo, footer_logo, and theme_mode columns to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding columns to custom_checkout_pages:', error);
    throw error;
  }
}

addLogoThemeToCheckout()
  .then(() => {
    console.log('Migration completed successfully');
    sqlite.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    sqlite.close();
    process.exit(1);
  });
