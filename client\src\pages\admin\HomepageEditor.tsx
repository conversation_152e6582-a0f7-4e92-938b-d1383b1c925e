import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getHomepageConfig, 
  updateHomepageConfig,
  updateSection,
  addSection,
  removeSection,
  reorderSections,
  resetHomepageConfig,
  createHeroSection,
  createFeaturesSection,
  createProductsSection,
  createCTASection,
  getSectionTypeLabel,
  getSectionIcon,
  type HomepageConfig,
  type PageSection 
} from '@/api/homepage';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Eye, 
  EyeOff, 
  Edit, 
  Trash2, 
  GripVertical, 
  RotateCcw,
  Save,
  ExternalLink,
  Loader2
} from 'lucide-react';

const HomepageEditor: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedSection, setSelectedSection] = useState<PageSection | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Fetch homepage configuration
  const { data: homepageConfig, isLoading, error } = useQuery<HomepageConfig>({
    queryKey: ['homepage-config'],
    queryFn: getHomepageConfig
  });

  // Update configuration mutation
  const updateConfigMutation = useMutation({
    mutationFn: updateHomepageConfig,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Homepage configuration updated successfully"
      });
      queryClient.invalidateQueries({ queryKey: ['homepage-config'] });
    },
    onError: (error) => {
      console.error('Failed to update homepage config:', error);
      toast({
        title: "Error",
        description: "Failed to update homepage configuration",
        variant: "destructive"
      });
    }
  });

  // Update section mutation
  const updateSectionMutation = useMutation({
    mutationFn: ({ sectionId, section }: { sectionId: string; section: PageSection }) => 
      updateSection(sectionId, section),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Section updated successfully"
      });
      queryClient.invalidateQueries({ queryKey: ['homepage-config'] });
      setIsEditing(false);
      setSelectedSection(null);
    },
    onError: (error) => {
      console.error('Failed to update section:', error);
      toast({
        title: "Error",
        description: "Failed to update section",
        variant: "destructive"
      });
    }
  });

  // Add section mutation
  const addSectionMutation = useMutation({
    mutationFn: addSection,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Section added successfully"
      });
      queryClient.invalidateQueries({ queryKey: ['homepage-config'] });
    },
    onError: (error) => {
      console.error('Failed to add section:', error);
      toast({
        title: "Error",
        description: "Failed to add section",
        variant: "destructive"
      });
    }
  });

  // Remove section mutation
  const removeSectionMutation = useMutation({
    mutationFn: removeSection,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Section removed successfully"
      });
      queryClient.invalidateQueries({ queryKey: ['homepage-config'] });
    },
    onError: (error) => {
      console.error('Failed to remove section:', error);
      toast({
        title: "Error",
        description: "Failed to remove section",
        variant: "destructive"
      });
    }
  });

  // Reset configuration mutation
  const resetConfigMutation = useMutation({
    mutationFn: resetHomepageConfig,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Homepage reset to default configuration"
      });
      queryClient.invalidateQueries({ queryKey: ['homepage-config'] });
    },
    onError: (error) => {
      console.error('Failed to reset homepage config:', error);
      toast({
        title: "Error",
        description: "Failed to reset homepage configuration",
        variant: "destructive"
      });
    }
  });

  // Handle section toggle
  const handleSectionToggle = (sectionId: string, enabled: boolean) => {
    const section = homepageConfig?.sections.find(s => s.id === sectionId);
    if (section) {
      updateSectionMutation.mutate({
        sectionId,
        section: { ...section, enabled }
      });
    }
  };

  // Handle add new section
  const handleAddSection = (type: PageSection['type']) => {
    const nextOrder = (homepageConfig?.sections.length || 0) + 1;
    const sectionId = `${type}-${Date.now()}`;
    
    let newSection: PageSection;
    
    switch (type) {
      case 'hero':
        newSection = createHeroSection(sectionId, nextOrder);
        break;
      case 'features':
        newSection = createFeaturesSection(sectionId, nextOrder);
        break;
      case 'products':
        newSection = createProductsSection(sectionId, nextOrder);
        break;
      case 'cta':
        newSection = createCTASection(sectionId, nextOrder);
        break;
      default:
        return;
    }
    
    addSectionMutation.mutate(newSection);
  };

  // Handle remove section
  const handleRemoveSection = (sectionId: string) => {
    if (confirm('Are you sure you want to remove this section?')) {
      removeSectionMutation.mutate(sectionId);
    }
  };

  // Handle reset configuration
  const handleResetConfig = () => {
    if (confirm('Are you sure you want to reset the homepage to default configuration? This will remove all customizations.')) {
      resetConfigMutation.mutate();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading homepage configuration...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
            <CardDescription>Failed to load homepage configuration</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>
              Retry
            </Button>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  const sortedSections = homepageConfig?.sections?.sort((a, b) => a.order - b.order) || [];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Homepage Editor</h1>
            <p className="text-gray-600 mt-1">
              Customize your homepage with drag-and-drop sections
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => window.open('/', '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Preview
            </Button>
            
            <Button
              variant="outline"
              onClick={handleResetConfig}
              disabled={resetConfigMutation.isPending}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset to Default
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{sortedSections.length}</div>
              <div className="text-sm text-gray-600">Total Sections</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {sortedSections.filter(s => s.enabled).length}
              </div>
              <div className="text-sm text-gray-600">Active Sections</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">v{homepageConfig?.version}</div>
              <div className="text-sm text-gray-600">Version</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {homepageConfig?.lastUpdated ? 
                  new Date(homepageConfig.lastUpdated).toLocaleDateString() : 
                  'Never'
                }
              </div>
              <div className="text-sm text-gray-600">Last Updated</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="sections" className="space-y-6">
          <TabsList>
            <TabsTrigger value="sections">Sections</TabsTrigger>
            <TabsTrigger value="seo">SEO Settings</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
          </TabsList>

          {/* Sections Tab */}
          <TabsContent value="sections" className="space-y-6">
            {/* Add Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Add New Section
                </CardTitle>
                <CardDescription>
                  Choose a section type to add to your homepage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { type: 'hero' as const, label: 'Hero Section', icon: '🎯', desc: 'Main banner with title and CTA' },
                    { type: 'features' as const, label: 'Features', icon: '⭐', desc: 'Highlight key features' },
                    { type: 'products' as const, label: 'Products', icon: '🛍️', desc: 'Showcase your products' },
                    { type: 'cta' as const, label: 'Call to Action', icon: '🚀', desc: 'Drive conversions' }
                  ].map((sectionType) => (
                    <Button
                      key={sectionType.type}
                      variant="outline"
                      onClick={() => handleAddSection(sectionType.type)}
                      disabled={addSectionMutation.isPending}
                      className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-gray-50"
                    >
                      <div className="text-2xl">{sectionType.icon}</div>
                      <div className="font-medium">{sectionType.label}</div>
                      <div className="text-xs text-gray-500 text-center">{sectionType.desc}</div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Sections List */}
            <Card>
              <CardHeader>
                <CardTitle>Homepage Sections</CardTitle>
                <CardDescription>
                  Manage the sections on your homepage. Drag to reorder.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sortedSections.map((section, index) => (
                    <div
                      key={section.id}
                      className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50"
                    >
                      {/* Drag Handle */}
                      <GripVertical className="h-5 w-5 text-gray-400 cursor-move" />
                      
                      {/* Section Info */}
                      <div className="flex-grow">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-lg">{getSectionIcon(section.type)}</span>
                          <span className="font-medium">{section.title}</span>
                          <Badge variant={section.enabled ? "default" : "secondary"}>
                            {getSectionTypeLabel(section.type)}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">
                          Order: {section.order} • {section.enabled ? 'Visible' : 'Hidden'}
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        {/* Toggle Visibility */}
                        <div className="flex items-center gap-2">
                          {section.enabled ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                          <Switch
                            checked={section.enabled}
                            onCheckedChange={(enabled) => handleSectionToggle(section.id, enabled)}
                            disabled={updateSectionMutation.isPending}
                          />
                        </div>
                        
                        {/* Edit Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedSection(section);
                            setIsEditing(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        {/* Delete Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveSection(section.id)}
                          disabled={removeSectionMutation.isPending}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  {sortedSections.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No sections configured. Add your first section above.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SEO Tab */}
          <TabsContent value="seo">
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
                <CardDescription>
                  Configure SEO meta tags for your homepage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  SEO settings editor coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Theme Tab */}
          <TabsContent value="theme">
            <Card>
              <CardHeader>
                <CardTitle>Theme Settings</CardTitle>
                <CardDescription>
                  Customize the visual appearance of your homepage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  Theme settings editor coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default HomepageEditor;
