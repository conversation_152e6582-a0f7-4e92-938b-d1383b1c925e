import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Activity, DollarSign, Package, Users } from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { TestDataCard } from '@/components/admin/TestDataCard';

export default function AdminDashboard() {
  // Fetch dashboard stats
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['/api/admin/stats'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/admin/stats');
        if (!response.ok) throw new Error('Failed to fetch stats');
        return response.json();
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Return default stats for demo purposes
        return {
          totalSales: 12890.45,
          totalOrders: 156,
          totalProducts: 12,
          recentOrders: [
            { id: 1, customerName: '<PERSON>', amount: '129.99', date: '2023-04-25', status: 'paid' },
            { id: 2, customerName: 'Jane Smith', amount: '89.99', date: '2023-04-24', status: 'pending' },
            { id: 3, customerName: 'Robert Johnson', amount: '199.99', date: '2023-04-23', status: 'paid' },
            { id: 4, customerName: 'Emily Davis', amount: '49.99', date: '2023-04-22', status: 'paid' },
            { id: 5, customerName: 'Michael Brown', amount: '149.99', date: '2023-04-21', status: 'cancelled' },
          ]
        };
      }
    }
  });

  // Stat cards to display
  const statCards = [
    {
      title: 'Total Revenue',
      value: stats?.totalSales
        ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(stats.totalSales)
        : '$0.00',
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      description: 'Total revenue from all sales',
    },
    {
      title: 'Orders',
      value: stats?.totalOrders || 0,
      icon: <Activity className="h-5 w-5 text-muted-foreground" />,
      description: 'Total number of orders',
    },
    {
      title: 'Products',
      value: stats?.totalProducts || 0,
      icon: <Package className="h-5 w-5 text-muted-foreground" />,
      description: 'Total active products',
    },
    {
      title: 'Customers',
      value: stats?.totalCustomers || 0,
      icon: <Users className="h-5 w-5 text-muted-foreground" />,
      description: 'Unique customers',
    }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Dashboard</h1>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                {card.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{card.value}</div>
                <p className="text-xs text-muted-foreground">{card.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Test Data Card */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <TestDataCard />
        </div>

        {/* Overview Tabs */}
        <Tabs defaultValue="orders" className="space-y-4">
          <TabsList>
            <TabsTrigger value="orders">Recent Orders</TabsTrigger>
            <TabsTrigger value="sales">Sales Overview</TabsTrigger>
            <TabsTrigger value="products">Top Products</TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full table-auto">
                    <thead>
                      <tr className="border-b text-left text-sm font-medium text-muted-foreground">
                        <th className="pb-2">Order ID</th>
                        <th className="pb-2">Customer</th>
                        <th className="pb-2">Amount</th>
                        <th className="pb-2">Date</th>
                        <th className="pb-2">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {isLoadingStats ? (
                        <tr>
                          <td colSpan={5} className="py-4 text-center">Loading recent orders...</td>
                        </tr>
                      ) : stats?.recentOrders?.length ? (
                        stats.recentOrders.map((order) => (
                          <tr key={order.id} className="border-b py-3 text-sm last:border-none">
                            <td className="py-3">#{order.id}</td>
                            <td className="py-3">{order.customerName}</td>
                            <td className="py-3">${order.amount}</td>
                            <td className="py-3">{order.date}</td>
                            <td className="py-3">
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                ${order.status === 'paid' ? 'bg-green-100 text-green-800' :
                                  order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800'}`}>
                                {order.status}
                              </span>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="py-4 text-center">No recent orders found</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sales" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sales Overview</CardTitle>
              </CardHeader>
              <CardContent className="h-80">
                <div className="h-full flex items-center justify-center">
                  <p className="text-muted-foreground">Sales chart will be displayed here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="products" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
              </CardHeader>
              <CardContent className="h-80">
                <div className="h-full flex items-center justify-center">
                  <p className="text-muted-foreground">Product performance chart will be displayed here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}