import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/components/admin/AdminLayout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Domain {
  id: number;
  name: string;
  domain: string;
  isMainDomain: boolean;
  businessFacadeId?: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface BusinessFacade {
  id: number;
  name: string;
  type: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  description?: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  imageUrl: string;
  active: boolean;
}

interface CheckoutPage {
  id: number;
  title: string;
  slug: string;
  productName: string;
  price: string;
  active: boolean;
}

const Domains: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [domainDialogOpen, setDomainDialogOpen] = useState(false);
  const [editingDomain, setEditingDomain] = useState<Domain | null>(null);
  const [domainFormData, setDomainFormData] = useState({
    name: '',
    domain: '',
    isMainDomain: false,
    businessFacadeId: '',
    active: true
  });
  const [selectedDomainId, setSelectedDomainId] = useState<number | null>(null);
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [checkoutDialogOpen, setCheckoutDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState('domains');

  // Fetch domains
  const { data: domains, isLoading: isLoadingDomains } = useQuery({
    queryKey: ['domains'],
    queryFn: async () => {
      const response = await axios.get('/api/domains');
      return response.data;
    }
  });

  // Fetch business facades
  const { data: businessFacades, isLoading: isLoadingFacades } = useQuery({
    queryKey: ['businessFacades'],
    queryFn: async () => {
      const response = await axios.get('/api/business-facades');
      return response.data;
    }
  });

  // Fetch products
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['products'],
    queryFn: async () => {
      const response = await axios.get('/api/products');
      return response.data;
    }
  });

  // Fetch domain products
  const { data: domainProducts, isLoading: isLoadingDomainProducts, refetch: refetchDomainProducts } = useQuery({
    queryKey: ['domainProducts', selectedDomainId],
    queryFn: async () => {
      if (!selectedDomainId) return [];
      const response = await axios.get(`/api/domains/${selectedDomainId}/products`);
      return response.data;
    },
    enabled: !!selectedDomainId
  });

  // Fetch checkout pages
  const { data: checkoutPages, isLoading: isLoadingCheckoutPages } = useQuery({
    queryKey: ['checkoutPages'],
    queryFn: async () => {
      const response = await axios.get('/api/custom-checkout');
      return response.data;
    }
  });

  // Fetch domain checkout pages
  const { data: domainCheckouts, isLoading: isLoadingDomainCheckouts, refetch: refetchDomainCheckouts } = useQuery({
    queryKey: ['domainCheckouts', selectedDomainId],
    queryFn: async () => {
      if (!selectedDomainId) return [];
      const response = await axios.get(`/api/domains/${selectedDomainId}/checkouts`);
      return response.data;
    },
    enabled: !!selectedDomainId
  });

  // Create domain mutation
  const createDomainMutation = useMutation({
    mutationFn: async (domain: any) => {
      const response = await axios.post('/api/domains', domain);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] });
      setDomainDialogOpen(false);
      resetDomainForm();
      toast({
        title: 'Success',
        description: 'Domain created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create domain',
        variant: 'destructive',
      });
    }
  });

  // Update domain mutation
  const updateDomainMutation = useMutation({
    mutationFn: async ({ id, domain }: { id: number, domain: any }) => {
      const response = await axios.put(`/api/domains/${id}`, domain);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] });
      setDomainDialogOpen(false);
      resetDomainForm();
      toast({
        title: 'Success',
        description: 'Domain updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update domain',
        variant: 'destructive',
      });
    }
  });

  // Delete domain mutation
  const deleteDomainMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await axios.delete(`/api/domains/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['domains'] });
      toast({
        title: 'Success',
        description: 'Domain deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete domain',
        variant: 'destructive',
      });
    }
  });

  // Add product to domain mutation
  const addProductToDomainMutation = useMutation({
    mutationFn: async ({ domainId, productId }: { domainId: number, productId: number }) => {
      const response = await axios.post(`/api/domains/${domainId}/products`, { productId });
      return response.data;
    },
    onSuccess: () => {
      refetchDomainProducts();
      setProductDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Product added to domain successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to add product to domain',
        variant: 'destructive',
      });
    }
  });

  // Remove product from domain mutation
  const removeProductFromDomainMutation = useMutation({
    mutationFn: async ({ domainId, productId }: { domainId: number, productId: number }) => {
      const response = await axios.delete(`/api/domains/${domainId}/products/${productId}`);
      return response.data;
    },
    onSuccess: () => {
      refetchDomainProducts();
      toast({
        title: 'Success',
        description: 'Product removed from domain successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to remove product from domain',
        variant: 'destructive',
      });
    }
  });

  // Add checkout page to domain mutation
  const addCheckoutToDomainMutation = useMutation({
    mutationFn: async ({ domainId, checkoutPageId }: { domainId: number, checkoutPageId: number }) => {
      const response = await axios.post(`/api/domains/${domainId}/checkouts`, { checkoutPageId });
      return response.data;
    },
    onSuccess: () => {
      refetchDomainCheckouts();
      setCheckoutDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Checkout page added to domain successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to add checkout page to domain',
        variant: 'destructive',
      });
    }
  });

  // Remove checkout page from domain mutation
  const removeCheckoutFromDomainMutation = useMutation({
    mutationFn: async ({ domainId, checkoutPageId }: { domainId: number, checkoutPageId: number }) => {
      const response = await axios.delete(`/api/domains/${domainId}/checkouts/${checkoutPageId}`);
      return response.data;
    },
    onSuccess: () => {
      refetchDomainCheckouts();
      toast({
        title: 'Success',
        description: 'Checkout page removed from domain successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to remove checkout page from domain',
        variant: 'destructive',
      });
    }
  });

  const resetDomainForm = () => {
    setDomainFormData({
      name: '',
      domain: '',
      isMainDomain: false,
      businessFacadeId: '',
      active: true
    });
    setEditingDomain(null);
  };

  const handleDomainFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setDomainFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleDomainSwitchChange = (name: string, checked: boolean) => {
    setDomainFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleDomainSelectChange = (name: string, value: string) => {
    setDomainFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDomainSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const domainData = {
      ...domainFormData,
      businessFacadeId: domainFormData.businessFacadeId ? parseInt(domainFormData.businessFacadeId) : undefined
    };

    if (editingDomain) {
      updateDomainMutation.mutate({ id: editingDomain.id, domain: domainData });
    } else {
      createDomainMutation.mutate(domainData);
    }
  };

  const handleEditDomain = (domain: Domain) => {
    setEditingDomain(domain);
    setDomainFormData({
      name: domain.name,
      domain: domain.domain,
      isMainDomain: domain.isMainDomain,
      businessFacadeId: domain.businessFacadeId ? domain.businessFacadeId.toString() : '',
      active: domain.active
    });
    setDomainDialogOpen(true);
  };

  const handleDeleteDomain = (id: number) => {
    deleteDomainMutation.mutate(id);
  };

  const handleAddProduct = (productId: number) => {
    if (selectedDomainId) {
      addProductToDomainMutation.mutate({ domainId: selectedDomainId, productId });
    }
  };

  const handleRemoveProduct = (productId: number) => {
    if (selectedDomainId) {
      removeProductFromDomainMutation.mutate({ domainId: selectedDomainId, productId });
    }
  };

  const handleAddCheckout = (checkoutPageId: number) => {
    if (selectedDomainId) {
      addCheckoutToDomainMutation.mutate({ domainId: selectedDomainId, checkoutPageId });
    }
  };

  const handleRemoveCheckout = (checkoutPageId: number) => {
    if (selectedDomainId) {
      removeCheckoutFromDomainMutation.mutate({ domainId: selectedDomainId, checkoutPageId });
    }
  };

  return (
    <AdminLayout>
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="domains">Domains</TabsTrigger>
            <TabsTrigger value="mappings">Domain Mappings</TabsTrigger>
          </TabsList>
          {selectedTab === 'domains' && (
            <Dialog open={domainDialogOpen} onOpenChange={setDomainDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetDomainForm}>Add Domain</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{editingDomain ? 'Edit Domain' : 'Add Domain'}</DialogTitle>
                  <DialogDescription>
                    {editingDomain ? 'Update domain details' : 'Add a new domain to your application'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleDomainSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">Name</Label>
                      <Input
                        id="name"
                        name="name"
                        value={domainFormData.name}
                        onChange={handleDomainFormChange}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="domain" className="text-right">Domain</Label>
                      <Input
                        id="domain"
                        name="domain"
                        value={domainFormData.domain}
                        onChange={handleDomainFormChange}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="businessFacadeId" className="text-right">Business Facade</Label>
                      <Select
                        value={domainFormData.businessFacadeId}
                        onValueChange={(value) => handleDomainSelectChange('businessFacadeId', value)}
                      >
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select a business facade" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">None</SelectItem>
                          {businessFacades?.map((facade: BusinessFacade) => (
                            <SelectItem key={facade.id} value={facade.id.toString()}>
                              {facade.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="isMainDomain" className="text-right">Main Domain</Label>
                      <div className="col-span-3 flex items-center space-x-2">
                        <Switch
                          id="isMainDomain"
                          checked={domainFormData.isMainDomain}
                          onCheckedChange={(checked) => handleDomainSwitchChange('isMainDomain', checked)}
                        />
                        <Label htmlFor="isMainDomain">Is main domain</Label>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="active" className="text-right">Active</Label>
                      <div className="col-span-3 flex items-center space-x-2">
                        <Switch
                          id="active"
                          checked={domainFormData.active}
                          onCheckedChange={(checked) => handleDomainSwitchChange('active', checked)}
                        />
                        <Label htmlFor="active">Domain is active</Label>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={createDomainMutation.isPending || updateDomainMutation.isPending}>
                      {editingDomain ? 'Update' : 'Add'} Domain
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <TabsContent value="domains">
          <Card>
            <CardHeader>
              <CardTitle>Domains</CardTitle>
              <CardDescription>Manage domains for your multi-domain setup</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingDomains ? (
                <div>Loading domains...</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Domain</TableHead>
                      <TableHead>Business Facade</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {domains?.map((domain: Domain) => (
                      <TableRow key={domain.id}>
                        <TableCell>
                          {domain.name}
                          {domain.isMainDomain && (
                            <Badge className="ml-2" variant="secondary">Main</Badge>
                          )}
                        </TableCell>
                        <TableCell>{domain.domain}</TableCell>
                        <TableCell>
                          {domain.businessFacadeId ? (
                            businessFacades?.find((f: BusinessFacade) => f.id === domain.businessFacadeId)?.name || 'Unknown'
                          ) : (
                            'None'
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant={domain.active ? 'default' : 'outline'}>
                            {domain.active ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm" onClick={() => handleEditDomain(domain)}>
                              Edit
                            </Button>
                            {!domain.isMainDomain && (
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="destructive" size="sm">Delete</Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This will permanently delete the domain and all its mappings.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleDeleteDomain(domain.id)}>
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mappings">
          <Card>
            <CardHeader>
              <CardTitle>Domain Mappings</CardTitle>
              <CardDescription>Manage product and checkout page mappings for each domain</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <Label htmlFor="domainSelect">Select Domain</Label>
                <Select
                  value={selectedDomainId?.toString() || ''}
                  onValueChange={(value) => setSelectedDomainId(value ? parseInt(value) : null)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a domain" />
                  </SelectTrigger>
                  <SelectContent>
                    {domains?.map((domain: Domain) => (
                      <SelectItem key={domain.id} value={domain.id.toString()}>
                        {domain.name} ({domain.domain})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedDomainId && (
                <Tabs defaultValue="products">
                  <TabsList className="mb-4">
                    <TabsTrigger value="products">Products</TabsTrigger>
                    <TabsTrigger value="checkouts">Checkout Pages</TabsTrigger>
                  </TabsList>

                  <TabsContent value="products">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">Products for this Domain</h3>
                      <Dialog open={productDialogOpen} onOpenChange={setProductDialogOpen}>
                        <DialogTrigger asChild>
                          <Button>Add Product</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Product to Domain</DialogTitle>
                            <DialogDescription>
                              Select a product to add to this domain
                            </DialogDescription>
                          </DialogHeader>
                          <div className="max-h-96 overflow-y-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Name</TableHead>
                                  <TableHead>Price</TableHead>
                                  <TableHead>Action</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {products?.filter((product: Product) => 
                                  !domainProducts?.some((dp: Product) => dp.id === product.id)
                                ).map((product: Product) => (
                                  <TableRow key={product.id}>
                                    <TableCell>{product.name}</TableCell>
                                    <TableCell>${product.price}</TableCell>
                                    <TableCell>
                                      <Button 
                                        size="sm" 
                                        onClick={() => handleAddProduct(product.id)}
                                      >
                                        Add
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    {isLoadingDomainProducts ? (
                      <div>Loading products...</div>
                    ) : domainProducts?.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        No products mapped to this domain yet
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {domainProducts?.map((product: Product) => (
                            <TableRow key={product.id}>
                              <TableCell>{product.name}</TableCell>
                              <TableCell>${product.price}</TableCell>
                              <TableCell>
                                <Button 
                                  variant="destructive" 
                                  size="sm"
                                  onClick={() => handleRemoveProduct(product.id)}
                                >
                                  Remove
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </TabsContent>

                  <TabsContent value="checkouts">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">Checkout Pages for this Domain</h3>
                      <Dialog open={checkoutDialogOpen} onOpenChange={setCheckoutDialogOpen}>
                        <DialogTrigger asChild>
                          <Button>Add Checkout Page</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Checkout Page to Domain</DialogTitle>
                            <DialogDescription>
                              Select a checkout page to add to this domain
                            </DialogDescription>
                          </DialogHeader>
                          <div className="max-h-96 overflow-y-auto">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Title</TableHead>
                                  <TableHead>Slug</TableHead>
                                  <TableHead>Action</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {checkoutPages?.filter((page: CheckoutPage) => 
                                  !domainCheckouts?.some((dc: CheckoutPage) => dc.id === page.id)
                                ).map((page: CheckoutPage) => (
                                  <TableRow key={page.id}>
                                    <TableCell>{page.title}</TableCell>
                                    <TableCell>{page.slug}</TableCell>
                                    <TableCell>
                                      <Button 
                                        size="sm" 
                                        onClick={() => handleAddCheckout(page.id)}
                                      >
                                        Add
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    {isLoadingDomainCheckouts ? (
                      <div>Loading checkout pages...</div>
                    ) : domainCheckouts?.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        No checkout pages mapped to this domain yet
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Title</TableHead>
                            <TableHead>Slug</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {domainCheckouts?.map((page: CheckoutPage) => (
                            <TableRow key={page.id}>
                              <TableCell>{page.title}</TableCell>
                              <TableCell>{page.slug}</TableCell>
                              <TableCell>
                                <Button 
                                  variant="destructive" 
                                  size="sm"
                                  onClick={() => handleRemoveCheckout(page.id)}
                                >
                                  Remove
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default Domains;
