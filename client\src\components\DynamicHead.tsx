import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getGeneralSettings } from '@/api/generalSettings';

const DynamicHead: React.FC = () => {
  // Fetch general settings
  const { data: settings } = useQuery({
    queryKey: ['generalSettings'],
    queryFn: getGeneralSettings,
    staleTime: 0, // Always fetch fresh data
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  useEffect(() => {
    if (settings) {
      // Update document title
      if (settings.siteName) {
        document.title = settings.siteName;
      }

      // Update favicon
      if (settings.faviconUrl) {
        const favicon = document.getElementById('favicon') as HTMLLinkElement;
        if (favicon) {
          favicon.href = settings.faviconUrl;
        } else {
          // Create favicon link if it doesn't exist
          const link = document.createElement('link');
          link.id = 'favicon';
          link.rel = 'icon';
          link.type = 'image/x-icon';
          link.href = settings.faviconUrl;
          document.head.appendChild(link);
        }
      }

      // Update meta description
      if (settings.siteDescription) {
        let metaDescription = document.querySelector('meta[name="description"]') as HTMLMetaElement;
        if (metaDescription) {
          metaDescription.content = settings.siteDescription;
        } else {
          metaDescription = document.createElement('meta');
          metaDescription.name = 'description';
          metaDescription.content = settings.siteDescription;
          document.head.appendChild(metaDescription);
        }
      }
    }
  }, [settings]);

  return null; // This component doesn't render anything
};

export default DynamicHead;
