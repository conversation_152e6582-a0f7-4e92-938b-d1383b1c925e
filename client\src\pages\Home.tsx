import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Product } from '@shared/schema';
import DynamicHomepage from '@/components/homepage/DynamicHomepage';

const Home: React.FC = () => {
  // Fetch products
  const { data: products, isLoading } = useQuery<Product[]>({
    queryKey: ['/api/products'],
  });

  return (
    <DynamicHomepage
      products={products || []}
      isLoadingProducts={isLoading}
    />
  );
};

export default Home;
