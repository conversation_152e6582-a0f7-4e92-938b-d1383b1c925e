import React, { ReactNode, createContext, useContext, useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface AdminAuthContextType {
  isAuthenticated: boolean | null;
  isLoading: boolean;
  login: (credentials: { username: string; password: string }) => void;
  logout: () => void;
  checkAuth: () => void;
  isPending: boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | null>(null);

export function AdminAuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  // Query to check authentication status
  const { data, refetch } = useQuery({
    queryKey: ['/api/admin/check-session'],
    queryFn: async () => {
      try {
        const response = await fetch('/api/admin/check-session', {
          credentials: 'include'
        });
        const data = await response.json();
        return data;
      } catch (error) {
        console.error('Error checking auth status:', error);
        return { isAuthenticated: false };
      }
    },
    retry: false,
    refetchOnWindowFocus: false,
  });
  
  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: { username: string; password: string }) => {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
        credentials: 'include',
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Login failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      refetch();
      toast({
        title: "Login successful",
        description: "Welcome to the admin dashboard",
      });
      setLocation('/admin/dashboard');
    },
    onError: (error) => {
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Invalid credentials",
        variant: "destructive"
      });
    }
  });
  
  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/admin/logout', {
        method: 'POST',
        credentials: 'include',
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Logout failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      refetch();
      toast({
        title: "Logout successful",
      });
      setLocation('/admin/login');
    },
    onError: (error) => {
      toast({
        title: "Logout failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    }
  });
  
  // Update authentication state when data changes
  useEffect(() => {
    if (data) {
      setIsAuthenticated(data.isAuthenticated);
      setIsLoading(false);
    }
  }, [data]);
  
  return (
    <AdminAuthContext.Provider value={{
      isAuthenticated,
      isLoading,
      login: loginMutation.mutate,
      logout: logoutMutation.mutate,
      checkAuth: refetch,
      isPending: loginMutation.isPending || logoutMutation.isPending
    }}>
      {children}
    </AdminAuthContext.Provider>
  );
}

export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (!context) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
}