import React from 'react';
import { Button } from '@/components/ui/button';
import { type PageSection, type HeroSection as HeroSectionType, type ThemeSettings } from '@/api/homepage';

interface HeroSectionProps {
  section: PageSection;
  theme?: ThemeSettings;
}

const HeroSection: React.FC<HeroSectionProps> = ({ section, theme }) => {
  const content = section.content as HeroSectionType;

  // Handle CTA click
  const handleCTAClick = () => {
    if (content.ctaLink) {
      if (content.ctaLink.startsWith('#')) {
        // Smooth scroll to anchor
        const element = document.querySelector(content.ctaLink);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        // Navigate to URL
        window.location.href = content.ctaLink;
      }
    }
  };

  // Generate background style
  const getBackgroundStyle = () => {
    switch (content.backgroundType) {
      case 'image':
        return {
          backgroundImage: content.backgroundImage ? `url(${content.backgroundImage})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        };
      case 'gradient':
        return {
          background: content.backgroundColor || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        };
      case 'solid':
        return {
          backgroundColor: content.backgroundColor || theme?.primaryColor || '#0070ba'
        };
      default:
        return {};
    }
  };

  return (
    <section 
      className="relative py-20 px-4 overflow-hidden"
      style={{
        ...getBackgroundStyle(),
        color: content.textColor || '#ffffff'
      }}
    >
      {/* Background overlay for better text readability */}
      {content.backgroundType === 'image' && (
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      )}

      <div className="container mx-auto text-center relative z-10">
        {/* Subtitle */}
        {content.subtitle && (
          <p 
            className="text-lg md:text-xl mb-4 opacity-90"
            style={{ color: content.textColor || '#ffffff' }}
          >
            {content.subtitle}
          </p>
        )}

        {/* Main Title */}
        <h1 
          className="text-4xl md:text-6xl font-bold mb-6 leading-tight"
          style={{ color: content.textColor || '#ffffff' }}
        >
          {content.title}
        </h1>

        {/* Description */}
        {content.description && (
          <p 
            className="text-lg md:text-xl mb-8 max-w-3xl mx-auto opacity-90"
            style={{ color: content.textColor || '#ffffff' }}
          >
            {content.description}
          </p>
        )}

        {/* Video */}
        {content.showVideo && content.videoUrl && (
          <div className="mb-8 max-w-4xl mx-auto">
            <div className="relative aspect-video rounded-lg overflow-hidden shadow-2xl">
              <iframe
                src={content.videoUrl}
                title="Hero Video"
                className="absolute inset-0 w-full h-full"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        )}

        {/* CTA Button */}
        {content.ctaText && (
          <div className="flex justify-center">
            <Button
              onClick={handleCTAClick}
              size="lg"
              className="text-lg px-8 py-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              style={{
                backgroundColor: theme?.accentColor || '#009cde',
                borderColor: theme?.accentColor || '#009cde',
                color: '#ffffff'
              }}
            >
              {content.ctaText}
            </Button>
          </div>
        )}
      </div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        {/* Floating shapes for visual interest */}
        <div 
          className="absolute top-20 left-10 w-20 h-20 rounded-full opacity-20 animate-pulse"
          style={{ backgroundColor: content.textColor || '#ffffff' }}
        ></div>
        <div 
          className="absolute bottom-20 right-10 w-16 h-16 rounded-full opacity-20 animate-pulse"
          style={{ 
            backgroundColor: content.textColor || '#ffffff',
            animationDelay: '1s'
          }}
        ></div>
        <div 
          className="absolute top-1/2 right-20 w-12 h-12 rounded-full opacity-20 animate-pulse"
          style={{ 
            backgroundColor: content.textColor || '#ffffff',
            animationDelay: '2s'
          }}
        ></div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg 
          className="w-6 h-6 opacity-70" 
          fill="none" 
          stroke={content.textColor || '#ffffff'} 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 14l-7 7m0 0l-7-7m7 7V3" 
          />
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;
