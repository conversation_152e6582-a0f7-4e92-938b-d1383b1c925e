/**
 * Email Templates Configuration
 * This file defines the structure for email templates
 * that can be customized through the admin interface.
 */

export interface EmailTemplate {
  id: string | number;
  name: string;
  description: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  category: string;
  isDefault: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface EmailTemplateCategory {
  id: string;
  name: string;
  description: string;
}

export interface EmailPlaceholder {
  name: string;
  description: string;
  example: string;
  category: string;
}

// Available placeholders for email templates
export const EMAIL_PLACEHOLDERS: EmailPlaceholder[] = [
  // Customer information
  {
    name: 'customerName',
    description: 'Customer\'s full name',
    example: '<PERSON>',
    category: 'customer'
  },
  {
    name: 'customerEmail',
    description: 'Customer\'s email address',
    example: '<EMAIL>',
    category: 'customer'
  },
  {
    name: 'country',
    description: 'Customer\'s country',
    example: 'United States',
    category: 'customer'
  },
  
  // Order information
  {
    name: 'orderNumber',
    description: 'Order number or ID',
    example: 'ORD-12345',
    category: 'order'
  },
  {
    name: 'productName',
    description: 'Name of the purchased product',
    example: 'Premium IPTV Subscription',
    category: 'order'
  },
  {
    name: 'price',
    description: 'Price of the product',
    example: '$19.99',
    category: 'order'
  },
  {
    name: 'date',
    description: 'Date of purchase',
    example: 'May 22, 2023',
    category: 'order'
  },
  {
    name: 'appType',
    description: 'Type of application',
    example: 'IPTV Smarters Pro',
    category: 'order'
  },
  {
    name: 'macAddress',
    description: 'MAC address (if applicable)',
    example: '00:1A:79:B4:E7:2D',
    category: 'order'
  },
  
  // System information
  {
    name: 'currentYear',
    description: 'Current year',
    example: '2023',
    category: 'system'
  },
  {
    name: 'upgradeLink',
    description: 'Link to upgrade from trial',
    example: 'https://example.com/upgrade',
    category: 'system'
  },
  {
    name: 'unsubscribeLink',
    description: 'Link to unsubscribe from emails',
    example: 'https://example.com/unsubscribe',
    category: 'system'
  },
  
  // Support information
  {
    name: 'ticketNumber',
    description: 'Support ticket number',
    example: 'TKT-5678',
    category: 'support'
  },
  {
    name: 'customerQuestion',
    description: 'Customer\'s question or issue',
    example: 'How do I reset my password?',
    category: 'support'
  },
  {
    name: 'supportResponse',
    description: 'Response from support team',
    example: 'You can reset your password by clicking the "Forgot Password" link on the login page.',
    category: 'support'
  }
];
