import { defineConfig } from "drizzle-kit";
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required. Please create a .env file with DATABASE_URL.");
}

// Determine database dialect based on the DATABASE_URL
const isSQLite = process.env.DATABASE_URL.startsWith('sqlite:');
const isMySQL = process.env.DATABASE_URL.startsWith('mysql:');

// Set the dialect based on the DATABASE_URL
let dialect = "postgresql"; // Default
if (isSQLite) {
  dialect = "sqlite";
} else if (isMySQL) {
  dialect = "mysql";
}

export default defineConfig({
  out: "./migrations",
  schema: "./shared/schema.ts",
  dialect: dialect,
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
});
