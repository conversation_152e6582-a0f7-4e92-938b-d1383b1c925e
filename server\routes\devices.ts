import express from 'express';
import { storage } from '../storage';
import { isAdmin } from '../middleware/auth';

const router = express.Router();

// Get all devices for the current user
router.get('/api/admin/devices', isAdmin, async (req, res) => {
  try {
    const userId = req.session.userId;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const devices = await storage.getDevices(userId);
    res.status(200).json(devices);
  } catch (error) {
    console.error('Error fetching devices:', error);
    res.status(500).json({ message: 'An error occurred while fetching devices' });
  }
});

// Add a new device
router.post('/api/admin/devices', isAdmin, async (req, res) => {
  try {
    const userId = req.session.userId;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { name } = req.body;
    if (!name) {
      return res.status(400).json({ message: 'Device name is required' });
    }

    // Get IP and user agent
    const ip = req.ip || req.socket.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';

    const device = await storage.addDevice(userId, {
      name,
      ip,
      userAgent
    });

    res.status(201).json(device);
  } catch (error) {
    console.error('Error adding device:', error);
    res.status(500).json({ message: 'An error occurred while adding the device' });
  }
});

// Remove a device
router.delete('/api/admin/devices/:deviceId', isAdmin, async (req, res) => {
  try {
    const userId = req.session.userId;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { deviceId } = req.params;
    const success = await storage.removeDevice(userId, deviceId);

    if (!success) {
      return res.status(404).json({ message: 'Device not found' });
    }

    res.status(200).json({ message: 'Device removed successfully' });
  } catch (error) {
    console.error('Error removing device:', error);
    res.status(500).json({ message: 'An error occurred while removing the device' });
  }
});

export default router;
